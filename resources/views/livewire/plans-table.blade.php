<div>
    <!-- Alert Container -->
    <div id="alert-container" class="mb-4 px-4 pt-4" style="display: none;">
        <div id="alert-message" class="p-4 rounded-md">
            <span id="alert-text"></span>
        </div>
    </div>

    <div class="customHeight overflow-y-scroll">

        <div class="flex items-center px-4 py-4 justify-between border-b  bg-white">
            <h1 class="whitespace-nowrap text-3xl font-medium">Plans</h1>
            <div class="flex gap-4">
                <!-- <img class="h-4 font-bold w-auto img px-1" src="{{ asset('images/MagnifyingGlass.svg') }}">
                <input wire:model.defer.live="search" type="text" class="p-4 outline-none w-full block border-1 border-black py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-md sm:leading-6" placeholder="Search"></input> -->
                <div class="input-container flex items-center">
                    <img class="search-icon h-4 w-auto" src="{{ asset('images/MagnifyingGlass.svg') }}"
                        alt="Search Icon">
                    <input wire:model.live.debounce.600ms="search" type="text"
                        class="p-4 outline-none w-full block border py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 rounded-lg placeholder:text-gray-400 focus:ring-2 focus:ring-inset sm:text-md sm:leading-6"
                        placeholder="Search">
                </div>

                <div class="relative z-20 w-full">
                    <div class="select-selected px-4 py-2 w-full border border-gray-300 rounded-lg z-auto shadow-sm cursor-pointer bg-white text-gray-500"
                        id="selectStatus">
                        All Statuses
                    </div>
                    <div
                        class="select-items absolute w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10 d-none">
                        <div class="flex items-center px-4 py-2 hover:bg-gray-100 ">
                            <input type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="all"
                                id="allStatus" wire:click="onSelectStatus('all')">
                            <label class="text-semibold p-1 rounded-lg px-2 text-xs block cursor-pointer"
                                for="allStatus">All Statuses</label>
                        </div>
                        <div class="flex items-center px-4 py-2 hover:bg-gray-100 ">
                            <input type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="active"
                                id="activeStatus" wire:model.defer="isActiveSelected" wire:click="onSelectStatus('active')"
                                @if (in_array('all', $this->selectedStatuses)) checked @endif>
                            <label class="activeColor p-1 rounded-lg px-2 text-xs block cursor-pointer"
                                for="activeStatus">Active</label>
                        </div>
                        <div class="flex items-center px-4 py-2 hover:bg-gray-100">
                            <input type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="draft"
                                id="draftStatus" wire:model.defer="isDraftSelected" wire:click="onSelectStatus('draft')"
                                @if (in_array('all', $this->selectedStatuses)) checked @endif>
                            <label class="DraftColor  p-1 rounded-lg px-2 text-xs cursor-pointer"
                                for="draftStatus">Draft</label>
                        </div>
                        <div class="flex items-center px-4 py-2 hover:bg-gray-100">
                            <input type="checkbox" class="mr-2 w-4 h-4 cursor-pointer checkbx" value="closed"
                                id="closedStatus" wire:model.defer="isClosedSelected" wire:click="onSelectStatus('closed')"
                                @if (in_array('all', $this->selectedStatuses)) checked @endif>
                            <label class="closedColor p-1 rounded-lg px-2 text-xs cursor-pointer"
                                for="closedStatus">Closed</label>
                        </div>
                    </div>
                </div>
                @if (auth()->user()->role != 'Viewer')
                <div class="flex-none rounded-full flex items-center justify-center hover:scale-125">
                <a href="{{ route('myChatbot.url') }}">
                    <img src="{{ asset('images/ai-2.png') }}" class="robot-p w-8 h-8" >
                    </a>
                </div>
                @endif

                @if (auth()->user()->role != 'Viewer')
                <div x-data="{
                    open: false,
                    step: @entangle('step'),
                    name: @entangle('name'), 
                    descriptions: @entangle('descriptions'),
                    selectedSectorsArr: [],
                    selectedIndustriesArr: [],
                    errors: {},
                    maxWords: 300,
                    constraints: {
                        name: {
                            presence: { allowEmpty: false, message: '^Name is required.' }
                        }
                    },

                    closeModal() {
                        this.open = false;
                        this.$dispatch('modalClosed');
                    },

                    wordCount(text) {
                        return text ? text.trim().split(/\s+/).length : 0;
                    },

                    // Computed property to show remaining words if within the limit
                    get remainingWords() {
                        const currentWordCount = this.wordCount(this.descriptions);
                        return currentWordCount <= this.maxWords ? this.maxWords - currentWordCount : null;
                    },

                    // Computed property for word limit error message if limit is exceeded
                    get wordLimitError() {
                        const currentWordCount = this.wordCount(this.descriptions);
                        console.log(currentWordCount)
                        return this.wordCount(this.descriptions) > this.maxWords 
                            ? `Description should not exceed ${this.maxWords} words. (Current count: ${currentWordCount})` 
                            : null;
                    },

                    validateStep1() {
                        let validationErrors = validate({ name: this.name }, this.constraints);
                        
                        if (!this.descriptions || this.descriptions.trim() === '') {
                            validationErrors = validationErrors || {};
                            validationErrors.descriptions = ['Description is required.'];
                        }

                        if (this.wordLimitError) {
                            validationErrors = validationErrors || {};
                            validationErrors.descriptions = [this.wordLimitError];
                        }

                        this.errors = Object.fromEntries(
                            Object.entries(validationErrors || {}).map(([field, messages]) => [field, messages[0]])
                        );

                        if (!Object.keys(this.errors).length) {
                            this.step = 2;
                        }
                    }
                }">

                    <!-- Trigger button inside the modal -->
                    <button x-on:click="open = true"
                        class="bg-cyan-500 flex items-center space-x-1 border border-gray-300 rounded-lg px-2 py-2 hover:bg-cyan-600 font-medium w-32 text-white text-md justify-center">
                        <img class="h-4 font-bold w-auto img px-1"
                            src="{{ asset('images/plus-white-without-circle.svg') }}">
                        <h1 class="text-md font-semibold">New Plan</h1>
                    </button>
                    <!-- Modal container -->
                    <!-- class="fixed inset-0 bg-white flex items-center justify-center z-50" style="display:none" x-transition.opacity -->
                    <div x-show="open" class="fixed inset-0 flex items-center justify-center backdrop-blur-sm z-50"
                        style="display:none">
                        <!-- Modal background with a higher z-index -->
                        <div class="modal-background fixed inset-0 bg-slate-400 opacity-50 z-40"></div>

                        <!-- Modal content with a lower z-index -->
                        <template x-if="step === 4">
                            <div
                                class="modal-content step1 relative border flex flex-col justify-center items-center border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">
                                <span class="py-3 px-3 rounded-full GreenBackground mainGreen text-center">
                                    <img class="w-6 h-6" src="{{ asset('images/right_tick_with_circle_green.svg') }}">
                                </span>
                                <h1 class="font-medium px-4 text-xl text-center my-5">New plan was successfully added!</h1>

                                <div class="w-full border-t my-5 border-gray-200"></div>

                                <div class="flex gap-2 w-full px-4">
                                    <button @click="open = false;step = 1" type="button"
                                        class="bg-white w-full text-black border p-2 rounded-md">Close</button>
                                    @if (!empty($plan))
                                    <a href="{{ route('plan.success_people.index', ['plan' => $plan->id]) }}"
                                        type="button"
                                        class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                        <span class="block"> Review</span>
                                    </a>
                                    @else
                                    <button @click="open = false" type="button"
                                        class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                        <span class="block"> Review</span>
                                    </button>
                                    @endif
                                </div>
                            </div>
                        </template>

                        <template x-if="step !== 4">
                            <div
                                class="modal-content step1 relative border border-gray-300 border-solid bg-white shadow-lg rounded-xl py-4 z-50 w-[30%]">
                                <img @click="open = false" class="absolute right top-2 w-auto cursor-pointer"
                                    src="{{ asset('images/cross.svg') }}" alt="Search Icon">
                                <h2 class="font-semibold px-4">Add New Plan</h2>
                                <div class="flex justify-between mt-3 px-4">
                                    <button @click="step = 1">
                                        <span
                                            class="mainBlue p-1 rounded-full px-2 text-xs"
                                            :class="{ 'BlueBackground mainBlue': step === 1, 'GreenBackground mainGreen': step > 1 }">1</span>
                                        <span class="text-xs font-medium">Plan Details</span>
                                    </button>
                                    <button @click="step = 2">
                                        <span
                                            class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs"
                                            :class="{ 'BlueBackground mainBlue': step === 2, 'GreenBackground mainGreen': step > 2 }">2</span>
                                        <span class="text-xs font-medium">Current Experience</span>
                                    </button>
                                    <button @click="step = 3">
                                        <span
                                            class="text-gray-400 grayBackground p-1 rounded-full px-2 text-xs"
                                            :class="{ 'BlueBackground mainBlue': step === 3, 'GreenBackground mainGreen': step > 3 }">3</span>
                                        <span class="text-xs font-medium">Additional Details</span>
                                    </button>
                                </div>
                                <div class="w-full border-t mt-3 border-gray-200"></div>

                                <template x-if="step === 1">
                                    <div class="h-full">
                                        <div class="h-5/6 flex items-center">
                                            <div class="w-full">
                                                <div class="relative py-2 bg-white">
                                                    <div class="absolute inset-0 flex items-center px-1" aria-hidden="true"></div>
                                                </div>
                                                <div class="modalscroll px-4">

                                                    <div class="mb-3">
                                                        <label for="name"
                                                            class="block text-xs font-medium labelcolor">Name <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                                        <input wire:model.defer="name" type="text" id="name"
                                                            placeholder="Enter plan name" name="name"
                                                            class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"
                                                            required />
                                                        <span x-show="errors.name" class="text-red-500 text-xs" x-text="errors.name"></span>

                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="description"
                                                            class="block text-xs font-medium labelcolor">Description <span class="text-red-500" style="font-size: 1rem;">*</span></label>
                                                        <textarea x-on:input="remainingWords" wire:model.defer="descriptions" id="description" name="description" placeholder="Enter plan description"
                                                            rows="3"
                                                            class="mt-1 placeholder:text-gray-400  block w-full p-2 outline-none border border-gray-300 rounded-md"></textarea>
                                                           <!-- Immediate error display if word limit is exceeded -->
                                                        <span x-show="wordLimitError" class="text-red-500 text-xs" x-text="wordLimitError"></span>
                                                        <span x-show="errors.descriptions && !wordLimitError" class="text-red-500 text-xs" x-text="errors.descriptions"></span>
                                                        <span x-show="errors.descriptions || wordLimitError"><br></span> <!-- Conditional line break -->
                                                        
                                                        <!-- Remaining word count display only if within the limit -->
                                                        <template x-if="!wordLimitError">
                                                            <span class="text-gray-500 text-xs">Remaining words: <span x-text="remainingWords"></span></span>
                                                        </template>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="tagged-role" class="mb-1 block text-xs font-medium labelcolor">Tagged Role</label>
                                                            <div wire:ignore x-data="{
															value: @entangle('selectedTaggedRole'),
															options: [],
															debounceTimeout: null, // Store debounce timer reference
															async fetchRoles(query = '') {
																let response = await fetch(`/api/roles?search=${encodeURIComponent(query)}`);
																this.options = await response.json();
															},
															init() {
																this.$nextTick(() => {
																	let choices = new Choices(this.$refs.selectTaggedRole, {
																		allowHTML: true,
																		placeholder: true,
																		placeholderValue: 'Select tagged role',
																	});
																	const refreshChoices = () => {
																		choices.clearStore();
																		choices.setChoices(
																			this.options.map(({ value, label }) => ({
																				value,
																				label,
																				selected: this.value === value,
																			}))
																		);
																	};

																	// Debounced search logic
																	const debouncedSearch = async (event) => {
																		const query = event.detail.value || '';
																		
																		// If query is empty, don't fetch
																		if (query.trim() === '') {
																			return;
																		}

																		clearTimeout(this.debounceTimeout); // Clear previous timer
																		this.debounceTimeout = setTimeout(async () => {
																			await this.fetchRoles(query); // Fetch roles after debounce
																			refreshChoices(); // Refresh choices
																		}, 300); // Wait 300ms after typing stops
																	};

																	// Add event listener only once
																	if (!this.debounceTimeout) {
																		this.$refs.selectTaggedRole.addEventListener('search', debouncedSearch);
																	}

																	this.$refs.selectTaggedRole.addEventListener('change', () => {
																		this.value = choices.getValue(true);
																	});

																	this.$watch('value', refreshChoices);
																	this.$watch('options', refreshChoices);

																	// Initial fetch to populate dropdown
																	this.fetchRoles();
																});
															},
														}">
															<select x-ref="selectTaggedRole"></select>
														</div>
                                                        @error('selectedTaggedRole')
                                                        <span class="text-red-500 text-xs">{{ $message }}</span>
                                                        @enderror
                                                    </div>

                                                    <div class="mb-3">
                                                        <label for="tagged-role"
                                                            class="mb-1 block text-xs font-medium labelcolor">Add
                                                            Colleague to Plan</label>
                                                        <div wire:ignore x-data="{
                                                            multiple: true,
                                                            value: @entangle('selectedColleagues'),
                                                            options: {{ json_encode($colleagues) }},
                                                            init() {
                                                                this.$nextTick(() => {
                                                                    let choices = new Choices(this.$refs.selectColleagues, {
                                                                        allowHTML: true,
                                                                        removeItems: true,
                                                                        removeItemButton: true,
                                                                        placeholder: true,
                                                                        placeholderValue: 'Select colleague(s)',
                                                                        classNames: {
                                                                            containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'], // Split into an array
                                                                            inputCloned: ['mb-0', 'p-0'],

                                                                        },
                                                                        noChoicesText: 'No colleague to choose from'
                                                                    })
                                                                    let refreshChoices = () => {
                                                                        let selection = this.multiple ? this.value : [this.value]
                                                        
                                                                        choices.clearStore()
                                                                        choices.setChoices(this.options.map(({ value, label }) => ({
                                                                            value,
                                                                            label,
                                                                            selected: selection.includes(value),
                                                                        })))
                                                                    }
                                                                    refreshChoices()
                                                        
                                                                    this.$refs.selectColleagues.addEventListener('change', () => {
                                                                        this.value = choices.getValue(true)
                                                                    })
                                                        
                                                                    this.$watch('value', () => refreshChoices())
                                                                    this.$watch('options', () => refreshChoices())
                                                                })
                                                            }
                                                        }"
                                                            class="text-black w-full">
                                                            <select x-ref="selectColleagues"
                                                                :multiple="multiple"></select>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="w-full border-t mt-4 border-gray-200"></div>

                                                <div class="flex gap-2 w-full px-4 mt-4 ">
                                                    <button @click="open = false" type="button"
                                                        class="bg-white w-full text-black border p-2 rounded-md">Cancel</button>
                                                    <button @click="validateStep1()" type="button"
                                                        class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md">
                                                        <span class="block"> Continue</span>
                                                        <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>

                                <template x-if="step === 2">
                                    <div>
                                        <div class="modalscroll px-4">
                                            <p class="text-sm font-normal labelcolor mt-3 mb-3">Enter any requirements
                                                you have for your Succession Plan below.</p>

                                            <div class="flex items-center justify-between mt-2">

                                                <div class="" x-data="{ newTargetRoles: '' }">
                                                    <div>
                                                        <label for="targetRoles"
                                                            class="text-xs font-medium labelcolor">Target Role</label>
                                                        <input type="text"
                                                            class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                            placeholder="Enter target role" x-model="newTargetRoles"
                                                            @keydown.enter.prevent="if(newTargetRoles !== '') { @this.newSkillData.targetRoles.push(newTargetRoles); newTargetRoles = ''; }"
                                                            @blur="if(newTargetRoles !== '') { @this.newSkillData.targetRoles.push(newTargetRoles); newTargetRoles = ''; }">
                                                    </div>
                                                </div>

                                                <div>
                                                    <label for="min_exp"
                                                        class="text-xs font-medium labelcolor">Minimum Tenure</label>
                                                    <input type="number" wire:model.defer="min_exp"
                                                        class="outline-none block text-center bg-white p-2 text-sm font-normal border border-gray-300 h-10 rounded-md w-32"
                                                        placeholder="0" name="min_exp">
                                                </div>


                                            </div>
                                            <!-- Display tags as individual elements -->
                                            <div class="mt-2 flex flex-wrap">
                                                <template x-for="(skill, index) in @this.newSkillData.targetRoles"
                                                    :key="index">
                                                    <span
                                                        class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                                        <span x-text="skill"></span>
                                                        <button type="button" class="ml-1"
                                                            @click="@this.newSkillData.targetRoles.splice(index, 1)">&times;</button>
                                                    </span>
                                                </template>
                                            </div>
                                            <div class="mt-2">
                                                <div class="" x-data="{ newStepUpCandidate: '' }">
                                                    <div>
                                                        <label for="stepUpCandidate"
                                                            class="text-xs font-medium labelcolor">Step-up
                                                            candidates</label>
                                                        <input type="text"
                                                            class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                            placeholder="Enter Step-up candidates"
                                                            x-model="newStepUpCandidate"
                                                            @keydown.enter.prevent="if(newStepUpCandidate !== '') { @this.newSkillData.stepUpCandidate.push(newStepUpCandidate); newStepUpCandidate = ''; }"
                                                            @blur="if(newStepUpCandidate !== '') { @this.newSkillData.stepUpCandidate.push(newStepUpCandidate); newStepUpCandidate = ''; }">

                                                        <div class="mt-2 flex flex-wrap">
                                                            <template
                                                                x-for="(skill, index) in @this.newSkillData.stepUpCandidate"
                                                                :key="index">
                                                                <span
                                                                    class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                                                    <span x-text="skill"></span>
                                                                    <button type="button" class="ml-1"
                                                                        @click="@this.newSkillData.stepUpCandidate.splice(index, 1)">&times;</button>
                                                                </span>
                                                            </template>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>

                                            <div class="mt-2">
                                                <div class="" x-data="{ newKeyword: '' }">
                                                    <div>
                                                        <label for="keyword"
                                                            class="text-xs font-medium labelcolor">Keyword
                                                        </label>
                                                        <input type="text"
                                                            class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                            placeholder="Enter keyword"
                                                            x-model="newKeyword"
                                                            @keydown.enter.prevent="if(newKeyword !== '') { @this.newSkillData.keyword.push(newKeyword); newKeyword = ''; }"
                                                            @blur="if(newKeyword !== '') { @this.newSkillData.keyword.push(newKeyword); newKeyword = ''; }">

                                                        <div class="mt-2 flex flex-wrap">
                                                            <template
                                                                x-for="(skill, index) in @this.newSkillData.keyword"
                                                                :key="index">
                                                                <span
                                                                    class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                                                    <span x-text="skill"></span>
                                                                    <button type="button" class="ml-1"
                                                                        @click="@this.newSkillData.keyword.splice(index, 1)">&times;</button>
                                                                </span>
                                                            </template>
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                            
                                            <!-- <div class="mt-2">
                                                <label for="planName" class="text-xs font-medium labelcolor">Sectors</label>
                                                <div wire:ignore x-data="{
                                                    multiple: true,
                                                    value: @entangle('selectedSectors'),
                                                    options: {{ json_encode($sectors) }},
                                                    init() {
                                                        this.$nextTick(() => {
                                                            let choices = new Choices(this.$refs.selectedSectors, {
                                                                allowHTML: true,
                                                                removeItems: true,
                                                                removeItemButton: true,
                                                                placeholder: true,
                                                                placeholderValue: 'Select sector(s)',
                                                                classNames: {
                                                                    containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'],
                                                                    inputCloned: ['mb-0', 'p-0']
                                                                },
                                                                noChoicesText: 'No sectors to choose from'
                                                            });

                                                            let refreshChoices = () => {
                                                                let selection = this.multiple ? this.value : [this.value];
                                                                choices.clearStore();
                                                                choices.setChoices(this.options.map(({ value, label }) => ({
                                                                    value,
                                                                    label,
                                                                    selected: selection.includes(value),
                                                                })));
                                                            };

                                                            refreshChoices();

                                                            this.$refs.selectedSectors.addEventListener('change', () => {
                                                                this.selectedSectorsArr = choices.getValue(true)
                                                                this.$dispatch('onSelectSectorAndIndustry');
                                                                this.value = choices.getValue(true)
                                                            })
                                                
                                                            this.$watch('value', () => refreshChoices())
                                                            this.$watch('options', () => refreshChoices())
                                                        })
                                                    }
                                                }" class="text-black w-full">
                                                    <select x-ref="selectedSectors" :multiple="multiple"></select>
                                                </div>
                                            </div> -->

                                            <div class="mt-2">
                                                <label for="planName" class="text-xs font-medium labelcolor">Industries</label>
                                                <div wire:ignore x-data="{
                                                    multiple: true,
                                                    value: @entangle('selectedIndustries'),
                                                    options: {{ json_encode($industries) }},
                                                    init() {
                                                        this.$nextTick(() => {
                                                            let choices = new Choices(this.$refs.selectedIndustries, {
                                                                allowHTML: true,
                                                                removeItems: true,
                                                                removeItemButton: true,
                                                                placeholder: true,
                                                                placeholderValue: 'Select industry(s)',
                                                                classNames: {
                                                                    containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'],
                                                                    inputCloned: ['mb-0', 'p-0']
                                                                },
                                                                noChoicesText: 'No industries to choose from'
                                                            });

                                                            let refreshChoices = () => {
                                                                let selection = this.multiple ? this.value : [this.value];
                                                                choices.clearStore();
                                                                choices.setChoices(this.options.map(({ value, label }) => ({
                                                                    value,
                                                                    label,
                                                                    selected: selection.includes(value),
                                                                })));
                                                            };

                                                            refreshChoices();

                                                            this.$refs.selectedIndustries.addEventListener('change', () => {
                                                                this.selectedIndustriesArr = choices.getValue(true)
                                                                this.$dispatch('onSelectSectorAndIndustry');
                                                                this.value = choices.getValue(true)
                                                            })
                                                
                                                            this.$watch('value', () => refreshChoices())
                                                            this.$watch('options', () => refreshChoices())
                                                        })
                                                    }
                                                }" class="text-black w-full">
                                                    <select x-ref="selectedIndustries" :multiple="multiple"></select>
                                                </div>
                                            </div>

                                            <div class="mt-2 mb-15">
                                                <label for="planName"
                                                    class="text-xs font-medium labelcolor">Companies</label>
                                                <!-- textarea for entering companies name -->
                                                <div wire:ignore x-data="{
                                                    multiple: true,
                                                    value: @entangle('selectedCompanies'),
                                                    options: {{ json_encode($companies) }},
                                                    allCompanies: {{ json_encode($companies) }},
                                                    init() {
                                                        this.$nextTick(() => {
                                                            let choices = new Choices(this.$refs.selectCompanies, {
                                                                allowHTML: true,
                                                                removeItems: true,
                                                                removeItemButton: true,
                                                                placeholder: true,
                                                                placeholderValue: 'Select company(s)',
                                                                classNames: {
                                                                    containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'],
                                                                    inputCloned: ['mb-0', 'p-0']
                                                                },
                                                                noChoicesText: 'No companies to choose from'
                                                            })
                                                            let refreshChoices = () => {
                                                                let selection = this.multiple ? this.value : [this.value]
                                                
                                                                choices.clearStore()
                                                                choices.setChoices(this.options.map(({ value, label }) => ({
                                                                    value,
                                                                    label,
                                                                    selected: selection.includes(value),
                                                                })))
                                                            }
                                                            refreshChoices()
                                                
                                                            this.$refs.selectCompanies.addEventListener('change', () => {
                                                                this.value = choices.getValue(true)
                                                            })
                                                
                                                            this.$watch('value', () => refreshChoices())
                                                            this.$watch('options', () => refreshChoices())

                                                            // Listen for the custom onSelectSectorAndIndustry event
                                                            document.addEventListener('onSelectSectorAndIndustry', event => {
                                                                this.options = this.filterCompaniesBasedOnSectorAndIndustries();
                                                                refreshChoices();
                                                            });
                                                        })
                                                    },
                                                    filterCompaniesBasedOnSectorAndIndustries() {

                                                        const selectedIndustries = this.selectedIndustriesArr;
                                                        const selectedSectors = this.selectedSectorsArr;

                                                        if((selectedIndustries && selectedIndustries.length > 0) && (selectedSectors && selectedSectors.length > 0)){
                                                            return this.allCompanies.filter(company => 
                                                                        selectedIndustries.includes(company.industry) && selectedSectors.includes(company.sector)
                                                                    );
                                                        }
                                                        else if(selectedIndustries && selectedIndustries.length > 0)
                                                            return this.allCompanies.filter(company => 
                                                                        selectedIndustries.includes(company.industry)
                                                                    );
                                                        else if(selectedSectors && selectedSectors.length > 0)
                                                            return this.allCompanies.filter(company => 
                                                                        selectedSectors.includes(company.sector)
                                                                    );
                                                        else {
                                                            return this.allCompanies;
                                                        }                                                            
                                                    }
                                                }" class="text-black w-full">
                                                    <select x-ref="selectCompanies" :multiple="multiple"></select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="w-full border-t mt-4 border-gray-200"></div>

                                        <div class="flex gap-2 w-full mt-4 px-4">
                                            <button @click="step = 1" type="button"
                                                class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                                                <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                                                <span class="block font-medium">Back</span>
                                            </button>
                                            <button @click="step = 3" type="button"
                                                class="text-white flex justify-center gap-5 items-center w-full bg-mainBlue p-2 rounded-md">
                                                <span class="block"> Continue</span>
                                                <img class="h-5 w-5" src="{{ asset('images/right-arrow.svg') }}">
                                            </button>
                                        </div>
                                    </div>
                                </template>

                                <template x-if="step === 3">
                                    <div>
                                        <div class="p-4 modalscroll3 pb-15">
                                            <p class="text-sm font-normal labelcolor mb-3">Enter any requirements you
                                                have for your Succession Plan below.</p>
                                            <div>
                                                <div class="mt-2">
                                                    <fieldset>
                                                        <legend class="text-xs font-medium labelcolor mb-1">Gender
                                                        </legend>

                                                        <!-- radio button in form of buttons -->
                                                        <ul class="donate-now flex gap-2">
                                                            <li>
                                                                <input type="radio" wire:model.defer="gender"
                                                                    id="male" name="gender" value="Male"
                                                                    class="cursor-pointer" />
                                                                <label for="male"
                                                                    class="text-center font-semibold labelcolor">Male</label>
                                                            </li>
                                                            <li>
                                                                <input type="radio" wire:model.defer="gender"
                                                                    id="female" name="gender" value="Female"
                                                                    class="cursor-pointer" />
                                                                <label for="female"
                                                                    class="text-center font-semibold labelcolor">Female</label>
                                                            </li>
                                                            <li>
                                                                <input type="radio" wire:model.defer="gender"
                                                                    id="not_required" name="gender"
                                                                    value="Not Applicable" class="cursor-pointer" />
                                                                <label for="not_required"
                                                                    class="text-center font-semibold labelcolor">Not
                                                                    Required</label>
                                                            </li>
                                                        </ul>
                                                    </fieldset>
                                                </div>

                                                <div class="mt-2">
                                                    <label for="country"
                                                        class="text-xs font-medium labelcolor mb-1">Country</label>
                                                    <div wire:ignore x-data="{
                                                        multiple: true,
                                                        value: @entangle('selectedCountries'),
                                                        options: {{ json_encode($countries) }},
                                                        init() {
                                                            this.$nextTick(() => {
                                                                let choices = new Choices(this.$refs.selectCountries, {
                                                                    removeItems: true,
                                                                    removeItemButton: true,
                                                                    placeholder: true,
                                                                    placeholderValue: 'Select country(s)',
                                                                    classNames: {
                                                                        containerInner: ['p-2', 'border', 'border-gray-300', 'rounded-md'], // Split into an array
                                                                        inputCloned: ['mb-0', 'p-0'],
                                                                        list: 'z-99'
                                                                    },
                                                                    noChoicesText: 'No countries to choose from'
                                                                })
                                                                let refreshChoices = () => {
                                                                    let selection = this.multiple ? this.value : [this.value]
                                                    
                                                                    choices.clearStore()
                                                                    choices.setChoices(this.options.map(({ value, label }) => ({
                                                                        value,
                                                                        label,
                                                                        selected: selection.includes(value),
                                                                    })))
                                                                }
                                                                refreshChoices()
                                                    
                                                                this.$refs.selectCountries.addEventListener('change', () => {
                                                                    this.value = choices.getValue(true)
                                                                })
                                                    
                                                                this.$watch('value', () => refreshChoices())
                                                                this.$watch('options', () => refreshChoices())
                                                            })
                                                        }
                                                    }"
                                                        class="text-black w-full">
                                                        <select x-ref="selectCountries"
                                                            :multiple="multiple"></select>
                                                    </div>
                                                </div>
                                                <div class="mt-4">
                                                    <fieldset>
                                                        <div class="flex justify-between">
                                                            <legend class="text-sm mb-2 font-normal text-gray-800">Is
                                                                Ethnicity important?</legend>
                                                            <div class="toggle-container">
                                                                <label class="relative cursor-pointer">
                                                                    <input type="checkbox" class="toggle-input"
                                                                        wire:model.defer="ethnicity" />
                                                                    <span class="toggle-slider"></span>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="flex-wrap">
                                                            <p class="text-xs font-normal labelcolor">Please note
                                                                profiling through ethnicity has a low accuracy only
                                                                select if this is absolutely neccessary</p>
                                                        </div>
                                                        <!-- no need of this -->
                                                        <!-- <div class="mt-1 flex space-x-3">
                                                                <div class="flex items-center gap-x-3">
                                                                    <input type="radio" wire:model.defer="ethnicity" class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black" value="1">
                                                                    <label for="push-yes" class="block text-xs leading-6 text-gray-900">Yes</label>
                                                                </div>
                                                                <div class="flex items-center gap-x-3">
                                                                    <input type="radio" wire:model.defer="ethnicity" class="h-4 w-4 rounded-full appearance-none shadow-inner shadow-gray-300 border border-gray-500 checked:border-4 checked:border-black" value="0">
                                                                    <label for="push-no" class="block text-xs text-gray-900">No</label>
                                                                </div>
                                                            </div> -->
                                                    </fieldset>
                                                </div>


                                                <div class="" x-data="{
                                                    newQualifications: '',
                                                    newSkills: ''
                                                }">
                                                    <div>
                                                        <!--
                                                        <label for="qualifications"
                                                            class="text-xs font-medium labelcolor">Qalifications</label>
                                                        <input type="text"
                                                            class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                            placeholder="Enter qualifications"
                                                            x-model="newQualifications"
                                                            @keydown.enter.prevent="if(newQualifications !== '') { @this.newSkillData.qualifications.push(newQualifications); newQualifications = ''; }"
                                                            @blur="if(newQualifications !== '') { @this.newSkillData.qualifications.push(newQualifications); newQualifications = ''; }">
                                                        -->
                                                        <div class="mt-2 flex flex-wrap">
                                                            <template
                                                                x-for="(skill, index) in @this.newSkillData.qualifications"
                                                                :key="index">
                                                                <span
                                                                    class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                                                    <span x-text="skill"></span>
                                                                    <button type="button" class="ml-1"
                                                                        @click="@this.newSkillData.qualifications.splice(index, 1)">&times;</button>
                                                                </span>
                                                            </template>
                                                        </div>
                                                    </div>


                                                    <div class="mt-4">
                                                        <label for="skills"
                                                            class="text-xs font-medium labelcolor">Skills</label>
                                                        <input type="text"
                                                            class="bg-white outline-none p-2 text-sm font-normal border border-gray-300 rounded-md text-gray-900 w-full hover:text-cyan-500"
                                                            placeholder="Enter skills" x-model="newSkills"
                                                            @keydown.enter.prevent="if(newSkills !== '') { @this.newSkillData.skills.push(newSkills); newSkills = ''; }"
                                                            @blur="if(newSkills !== '') { @this.newSkillData.skills.push(newSkills); newSkills = ''; }">

                                                        <div class="mt-2 flex flex-wrapsummary">
                                                            <template
                                                                x-for="(skill, index) in @this.newSkillData.skills"
                                                                :key="index">
                                                                <span
                                                                    class="selectedBg bg-gray-200 text-gray-700 px-2 py-1 rounded-full mr-2 mb-1">
                                                                    <span x-text="skill"></span>
                                                                    <button type="button" class="ml-1"
                                                                        @click="@this.newSkillData.skills.splice(index, 1)">&times;</button>
                                                                </span>
                                                            </template>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- border above buttons -->
                                        <div class="w-full border-t  border-gray-200"></div>

                                        <!-- 3rd steps button -->
                                        <div class="flex gap-2 w-full mt-4 px-4">
                                            <button @click="step = 2" type="button"
                                                class="bg-white w-full text-black border p-2 flex justify-center gap-5 items-center rounded-md">
                                                <img class="h-5 w-5" src="{{ asset('images/BackArrow.svg') }}">
                                                <span class="block font-medium">Back</span>
                                            </button>
                                            <button type="button" wire:click="CreatePlan"
                                                class=" text-white flex justify-center gap-5 items-center w-full bg-mainBlue  p-2 rounded-md">
                                                <img class="h-5 w-5"
                                                    src="{{ asset('images/plus-white-without-circle.svg') }}">
                                                <span class="block">Add Plan</span>
                                            </button>
                                        </div>

                                    </div>
                                </template>
                            </div>
                        </template>
                    </div>

                </div>
                @endif
            </div>
        </div>
        <div class="my-5 px-4">
          
            @if (!empty($plans))
            <ul role="list" class="grid grid-cols-1 sm:grid-cols-2 gap-6 lg:grid-cols-3 z-30">
                @foreach ($plans as $plan)
                <li
                    class="col-span-1 divide-gray-200 border-solid border border-grey-200 rounded-2xl bg-white shadow-md">
                    <div class="flex w-full h-5/6 justify-between mb-3">
                        <div class="flex-1 flex-col flex h-full p-4 relative">
                            <!-- Title and Status Row -->
                            <div class="flex justify-between items-start mb-2">
                                <div class="flex-1 pr-4">
                                    <h3 class="text-lg font-medium text-gray-900 leading-tight">{{ $plan->name }}</h3>
                                </div>
                                <div class="flex items-center space-x-2 flex-shrink-0">
                                    <span class="inline-flex flex-shrink-0 items-center rounded-lg px-3 py-2 text-xs font-medium 
                                        @if ($plan->status == 'Active') bg-green-50 text-green-700 ring-green-600/20
                                        @elseif($plan->status == 'Closed') bg-red-50 text-red-700 ring-red-600/20
                                        @elseif($plan->status == 'Draft') bg-blue-50 text-cyan-700 ring-cyan-300 @endif">
                                        {{ $plan->status }}
                                    </span>
                                    @if($plan->mover && $plan->mover == 'Mover')
                                        <img src="{{ asset('images/bell_red.svg') }}" class="ml-0" alt="">
                                    @endif
                                </div>
                            </div>

                            <!-- Created by Row -->
                            @php $isMaster = (auth()->user()->role === 'master') || (str_contains(auth()->user()->email, 'master')); @endphp
                            @if($isMaster)
                                <div class="flex items-center justify-between mb-3 pb-2 border-b border-gray-100">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                                            <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 016 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                            </svg>
                                        </div>
                                        <span class="text-sm text-gray-600">Created by <span class="font-semibold">{{ $plan->user->email ?? 'Unknown' }}</span></span>
                                    </div>
                                    <!-- Dropdown menu stays on the right -->
                                    @if (auth()->user()->role != 'Viewer')
                                        <div class="dropdown-container relative">
                                            <button tabindex="1" id="dropdownDefaultButton"
                                                data-dropdown-toggle="dropdown"
                                                class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm py-2.5 text-center inline-flex items-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"
                                                type="button">
                                                <img class="h-5 w-5"
                                                    src="{{ asset('images/DotsThreeVertival.svg') }}">
                                            </button>

                                            <!-- Dropdown menu -->
                                            <div id="dropdown"
                                                class="dropdown-menu absolute bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                                                <ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
                                                    <li class="cursor-pointer">
                                                        <a href="{{ route('plan.show', ['plan' => $plan->id]) }}"
                                                            class="p-0">
                                                            <div
                                                                class="flex justify-center gap-4 px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                <img class="h-5 w-5"
                                                                    src="{{ asset('images/editicon.svg') }}">
                                                                <span class="font-semibold text-sm">
                                                                    Plan Designer
                                                                </span>
                                                            </div>
                                                        </a>
                                                    </li>
                                                    <li class="cursor-pointer">
                                                        <div class="p-0" wire:click.prevent="rerunPlan({{ $plan->id }})" onclick="event.preventDefault(); event.stopPropagation();">
                                                            <div
                                                                class="flex justify-center gap-4 px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                                </svg>
                                                                <span class="font-semibold text-sm">
                                                                    Rerun Plan
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li class="cursor-pointer">
                                                        <a class="p-0" wire:click="showExecutionHistory({{ $plan->id }})">
                                                            <div
                                                                class="flex justify-center gap-4 px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                                <span class="font-semibold text-sm">
                                                                    Execution History
                                                                </span>
                                                            </div>
                                                        </a>
                                                    </li>
                                                    <li class="cursor-pointer">
                                                        <a class="p-0" wire:click="showScheduleModal({{ $plan->id }})">
                                                            <div
                                                                class="flex justify-center gap-4 px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                                </svg>
                                                                <span class="font-semibold text-sm">
                                                                    Schedule Plan
                                                                </span>
                                                            </div>
                                                        </a>
                                                    </li>
                                                    @if (auth()->user()->id == $plan->user_id)
                                                        <li class="cursor-pointer">
                                                            <a class="p-0"
                                                                @click="confirmDelete({{ $plan->id }}, '{{ $plan->name }}', '{{ asset('images/redTrashIcon.svg') }}')">
                                                                <div
                                                                    class="flex gap-5 py-2 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                                                                    <img class="h-5 w-5"
                                                                        src="{{ asset('images/deleteIcon.svg') }}">
                                                                    <span class="font-semibold text-sm">Delete</span>
                                                                </div>
                                                            </a>
                                                        </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            @endif
                            <div class="mt-1">
                                <p
                                    class="h-24 whitespace-wrap mt-1 text-sm font-light text-gray-500 plan-description">
                                    {{ $plan->description }}
                                </p>
                            </div>
     
                            <div class="mt-1">
                                <p
                                    class="whitespace-wrap mt-1 text-sm  text-gray-500 plan-description">
                                    <span class="">Date created: </span>{{$plan->created_at}}
                                </p>
                                <p class="whitespace-wrap mt-1 text-sm  text-gray-500 plan-description">
                                <span class="">Date updated:</span> {{$plan->updated_at}}
                                </p>
                            </div>
                            <div class="mt-4 items-end grid grid-cols-2 gap-2">
                                <div>
                                    <dl class="text-gray-700 text-md whitespace-nowrap inline-flex">
                                        <img class="lh-8 h-5 w-auto"
                                            src="{{ asset('images/peoples.svg') }}">&nbsp;&nbsp;Pipeline
                                        People
                                    </dl>
                                </div>
                                <div>
                                    <dd class="text-gray-700 text-md whitespace-nowrap text-right">
                                        {{ $plan->pipelinecount }}
                                    </dd>
                                </div>
                            </div>
                            <div class="mt-2 items-end grid grid-cols-2 gap-2">
                                <div>
                                    <dl class="text-gray-700 text-md whitespace-nowrap inline-flex">
                                        <img class="lh-8 h-5 w-auto"
                                            src="{{ asset('images/CheckCircle.svg') }}">&nbsp;&nbsp;Succession
                                        Candidates
                                    </dl>
                                </div>
                                <div>
                                    <dd class="text-gray-700 text-md whitespace-nowrap text-right">
                                        {{ $plan->successcount }}
                                    </dd>
                                </div>
                            </div>

                            @if ($plan->combinedScoreData)
                            @foreach ($plan->combinedScoreData as $key => $scoreData)
                            <div class="mt-3 items-end grid grid-cols-2 gap-2">
                                <div>
                                    <dl class="text-gray-500 text-sm whitespace-nowrap">
                                        @if ($key === 'Internal-External Ratio')
                                        Internal
                                        @else
                                        {{ $key }}
                                        @endif
                                    </dl>
                                </div>
                                <div class="flex">
                                    <progress
                                        class="progress 
                                                                        @if ($key == 'Female-ratio' || $key == 'Female-Ratio') progress-orange
                                                                        @elseif($key == 'Male-Ratio' || $key == 'Male-ratio')
                                                                            progress-blue
                                                                        @elseif($key == 'Internal-External Ratio')
                                                                            progress-green
                                                                        @elseif($key == 'Skill Score')
                                                                            progress-purple
                                                                        @else
                                                                            progress-sky @endif"
                                        max="100" value="{{ $scoreData }}"></progress>
                                    <dd class="text-gray-500 text-sm whitespace-nowrap text-right">
                                        &nbsp;&nbsp;{{ $scoreData }}%</dd>
                                </div>
                            </div>
                            @endforeach
                            @else
                            <div class="mt-4 items-end grid grid-cols-2 gap-2">
                                <div>
                                    <dl class="text-gray-500 text-sm whitespace-nowrap">Female-Ratio</dl>
                                </div>
                                <div class="flex">
                                    <progress class="progress progress-orange" max="100"
                                        value="0"></progress>
                                    <dd class="text-gray-500 text-sm whitespace-nowrap text-right">
                                        &nbsp;&nbsp;0%</dd>
                                </div>
                            </div>
                            <div class="mt-2 items-end grid grid-cols-2 gap-2">
                                <div>
                                    <dl class="text-gray-500 text-sm whitespace-nowrap">Male-Ratio</dl>
                                </div>
                                <div class="flex">
                                    <progress class="progress progress-blue" max="100"
                                        value="0"></progress>
                                    <dd class="text-gray-500 text-sm whitespace-nowrap text-right">
                                        &nbsp;&nbsp;0%</dd>
                                </div>
                            </div>
                            <div class="mt-2 items-end grid grid-cols-2 gap-2">
                                <div>
                                    <dl class="text-gray-500 text-sm whitespace-nowrap">Internal</dl>
                                </div>
                                <div class="flex">
                                    <progress class="progress progress-green" max="100"
                                        value="0"></progress>
                                    <dd class="text-gray-500 text-sm whitespace-nowrap text-right">
                                        &nbsp;&nbsp;0%</dd>
                                </div>
                            </div>
                            <div class="mt-2 items-end grid grid-cols-2 gap-2">
                                <div>
                                    <dl class="text-gray-500 text-sm whitespace-nowrap">Skill Score</dl>
                                </div>
                                <div class="flex">
                                    <progress class="progress progress-purple" max="100"
                                        value="0"></progress>
                                    <dd class="text-gray-500 text-sm whitespace-nowrap text-right">
                                        &nbsp;&nbsp;0%</dd>
                                </div>
                            </div>
                            <div class="mt-2 items-end grid grid-cols-2 gap-2">
                                <div>
                                    <dl class="text-gray-500 text-sm whitespace-nowrap">Tenure Score</dl>
                                </div>
                                <div class="flex">
                                    <progress class="progress progress-sky" max="100"
                                        value="0"></progress>
                                    <dd class="text-gray-500 text-sm whitespace-nowrap text-right">
                                        &nbsp;&nbsp;0%</dd>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    @if ($plan->successcount > 0)
                    <div class="px-4">
                        <a href="{{ route('plan.final.index', ['plan' => $plan->id]) }}" target="_blank"
                            class="bg-white flex items-center space-x-1 border border-gray-300 rounded-lg px-2 py-2 hover:bg-gray-200 font-medium text-black text-md justify-center w-full">
                            <h1 class="text-md font-semibold">View Report</h1>
                        </a>
                    </div>
                    @endif
                </li>
                @endforeach
            </ul>
            @else
            <ul role="list" class="grid grid-cols-1 z-30">
                <li class="col-span-1 divide-gray-200 text-center my-5 py-5">
                    <h5>No record found</h5>
                </li>
            </ul>
            @endif

        </div>
    </div>
    @include('livewire.loading')
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Listen for the 'roleUpdated' event emitted by Livewire
        Livewire.on('optionsUpdated', (optionsUpdated) => {
            choices = optionsUpdated[0];

            setTimeout(() => {

            // Perform your logic here, e.g., update the Choices.js options
            if (window.myChoicesInstance) {
                window.myChoicesInstance.clearChoices()

                window.myChoicesInstance.setChoices(
                    choices,
                    'value',
                    'label',
                    false,
                );
            }
        }, 2000);
        });
    });
</script>



<script>
    document.addEventListener("DOMContentLoaded", function() {
        var selectStatus = document.getElementById('selectStatus');
        var selectItems = selectStatus.nextElementSibling;

        selectStatus.addEventListener('click', function() {
            if (selectItems.classList.contains('d-none')) {
                selectItems.classList.remove('d-none');
            } else {
                selectItems.classList.add('d-none');
            }
        });
    });

    function confirmDelete(planId, planName, iconUrl) {
        Swal.fire({
            // title: "Do you want to delete this plan?",
            html: `<div class="px-5 w-full flex justify-center mb-3"><img class="h-10 w-10" src="${iconUrl}" alt=""></div>
                    <h2 class="px-5 text-2xl text-center text-black font-medium mb-4">Delete ${planName}</h2>
                    <p class="px-5 font-normal">Are you sure want to delete the plan <b>${planName}</b>?</p>
                    <div class="w-full border-t mt-5 border-gray-200"></div>`,
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: "Delete",
            denyButtonText: `Cancel`,
            reverseButtons: true,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'bg-white btnsWidth text-red-500 font-semibold border p-2 flex justify-center gap-5 items-center rounded-md', // Custom class for confirm button
                denyButton: 'bg-white btnsWidth  btnsmargin  text-black font-semibold border p-2 flex justify-center gap-5 items-center rounded-md' // Custom class for cancel button
            },
            showCloseButton: true
        }).then((result) => {
            /* Read more about isConfirmed, isDenied below */
            if (result.isConfirmed) {
                Livewire.dispatch('deleteplan', {
                    planId: planId
                });
            }
        });
    }

    // Listen for Livewire alert events
    document.addEventListener('livewire:init', () => {
        Livewire.on('show-alert', (event) => {
            const alertContainer = document.getElementById('alert-container');
            const alertMessage = document.getElementById('alert-message');
            const alertText = document.getElementById('alert-text');

            if (alertContainer && alertMessage && alertText) {
                // Set alert type styling
                alertMessage.className = 'p-4 rounded-md';
                if (event.type === 'success') {
                    alertMessage.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
                } else if (event.type === 'error') {
                    alertMessage.classList.add('bg-red-100', 'border', 'border-red-400', 'text-red-700');
                }

                // Set alert text
                alertText.textContent = event.message;

                // Show alert
                alertContainer.style.display = 'block';

                // Auto-hide after 5 seconds
                setTimeout(() => {
                    alertContainer.style.display = 'none';
                }, 5000);
            }
        });
    });

</script>