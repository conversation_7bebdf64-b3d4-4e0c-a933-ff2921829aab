<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plan_executions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('succession_plan_id');
            $table->unsignedBigInteger('user_id');
            $table->string('execution_type')->default('manual'); // 'manual', 'scheduled'
            $table->string('status')->default('pending'); // 'pending', 'running', 'completed', 'failed'
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->integer('internal_candidates_found')->default(0);
            $table->integer('external_candidates_found')->default(0);
            $table->integer('total_candidates_found')->default(0);
            $table->text('error_message')->nullable();
            $table->json('execution_metadata')->nullable(); // Store search parameters, etc.
            $table->timestamps();

            $table->foreign('succession_plan_id')->references('id')->on('succession_plans')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            $table->index(['succession_plan_id', 'created_at']);
            $table->index(['status']);
            $table->index(['execution_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plan_executions');
    }
};
