<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pipelines', function (Blueprint $table) {
            $table->unsignedBigInteger('execution_id')->nullable()->after('plan_id');
            $table->index(['execution_id']);
            
            // Add foreign key constraint
            $table->foreign('execution_id')->references('id')->on('plan_executions')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pipelines', function (Blueprint $table) {
            $table->dropForeign(['execution_id']);
            $table->dropIndex(['execution_id']);
            $table->dropColumn('execution_id');
        });
    }
};
