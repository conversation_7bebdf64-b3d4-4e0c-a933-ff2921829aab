<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plan_schedules', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('succession_plan_id');
            $table->unsignedBigInteger('user_id');
            $table->string('frequency'); // 'daily', 'weekly', 'monthly'
            $table->string('frequency_value')->nullable(); // For weekly: 'monday', for monthly: '1' (day of month)
            $table->time('execution_time')->default('09:00:00'); // Time of day to run
            $table->string('timezone')->default('UTC');
            $table->boolean('is_active')->default(true);
            $table->timestamp('next_execution_at')->nullable();
            $table->timestamp('last_execution_at')->nullable();
            $table->timestamp('last_attempted_at')->nullable();
            $table->integer('total_executions')->default(0);
            $table->integer('missed_executions')->default(0);
            $table->integer('consecutive_failures')->default(0);
            $table->timestamp('last_failure_at')->nullable();
            $table->boolean('catch_up_enabled')->default(true);
            $table->integer('max_catch_up_executions')->default(3);
            $table->timestamps();

            $table->foreign('succession_plan_id')->references('id')->on('succession_plans')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            $table->index(['is_active', 'next_execution_at']);
            $table->index(['succession_plan_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plan_schedules');
    }
};
