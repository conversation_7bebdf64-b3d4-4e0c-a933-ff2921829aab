<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Plan Scheduling Monitoring Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for monitoring and resilience of the plan scheduling system
    |
    */

    // Downtime detection settings
    'downtime' => [
        'threshold_minutes' => env('PLAN_DOWNTIME_THRESHOLD', 30),
        'auto_catchup_enabled' => env('PLAN_AUTO_CATCHUP', true),
        'max_catchup_executions' => env('PLAN_MAX_CATCHUP', 3),
    ],

    // Failure handling settings
    'failures' => [
        'max_consecutive_failures' => env('PLAN_MAX_FAILURES', 5),
        'auto_disable_on_failure' => env('PLAN_AUTO_DISABLE', true),
        'failure_notification_threshold' => env('PLAN_FAILURE_THRESHOLD', 3),
    ],

    // Rate limiting settings
    'rate_limiting' => [
        'max_concurrent_executions' => env('PLAN_MAX_CONCURRENT', 3),
        'max_concurrent_catchup' => env('PLAN_MAX_CONCURRENT_CATCHUP', 2),
        'execution_timeout_minutes' => env('PLAN_EXECUTION_TIMEOUT', 60),
    ],

    // Monitoring settings
    'monitoring' => [
        'health_check_interval' => env('PLAN_HEALTH_CHECK_INTERVAL', 30), // minutes
        'log_retention_days' => env('PLAN_LOG_RETENTION', 30),
        'alert_cooldown_minutes' => env('PLAN_ALERT_COOLDOWN', 60),
    ],

    // Notification settings
    'notifications' => [
        'admin_emails' => explode(',', env('PLAN_ADMIN_EMAILS', '<EMAIL>')),
        'enable_email_alerts' => env('PLAN_EMAIL_ALERTS', true),
        'enable_slack_alerts' => env('PLAN_SLACK_ALERTS', false),
        'slack_webhook_url' => env('PLAN_SLACK_WEBHOOK'),
    ],

    // Recovery settings
    'recovery' => [
        'auto_recovery_enabled' => env('PLAN_AUTO_RECOVERY', true),
        'recovery_delay_minutes' => env('PLAN_RECOVERY_DELAY', 5),
        'max_recovery_attempts' => env('PLAN_MAX_RECOVERY_ATTEMPTS', 3),
    ],

    // Dashboard settings
    'dashboard' => [
        'refresh_interval_seconds' => env('PLAN_DASHBOARD_REFRESH', 30),
        'show_detailed_logs' => env('PLAN_SHOW_DETAILED_LOGS', true),
        'max_log_entries' => env('PLAN_MAX_LOG_ENTRIES', 100),
    ],
];
