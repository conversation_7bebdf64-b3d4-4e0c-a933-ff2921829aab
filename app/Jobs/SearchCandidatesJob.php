<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\AI\InternalPeopleSearch;
use App\Services\AI\ExternalPeopleSearch;
use App\Services\Queue\SearchQueueManager;
use App\Jobs\Traits\ExponentialBackoffRetry;
use App\Models\PlanExecution;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class SearchCandidatesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ExponentialBackoffRetry;

    // Set a longer timeout for this specific job
    public $timeout = 1800; // 30 minutes
    
    // Retry configuration for master orchestration job
    public $tries = 5;

    // Search types
    const SEARCH_INTERNAL = 'internal';
    const SEARCH_EXTERNAL = 'external';
    const SEARCH_BOTH = 'both';

    protected $planData;
    protected $user;
    protected $searchType;

    /**
     * Create a new job instance.
     *
     * @param array $planData The succession plan data
     * @param object $user The current user
     * @param string $searchType The type of search to perform (internal, external, or both)
     */
    public function __construct($planData, $user, $searchType = self::SEARCH_BOTH)
    {
        $this->planData = $planData;
        $this->user = $user;
        $this->searchType = $searchType;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info("SEARCH JOB: Starting search job for plan '{$this->planData['plan_name']}' with search type: {$this->searchType}");

        // Log queue configuration for this search
        SearchQueueManager::logQueueConfiguration();

        // Validate plan data and set default values for optional fields
        $this->validateAndPreparePlanData();

        $totalCandidates = 0;
        $internalCandidates = 0;
        $externalCandidates = 0;

        try {
            // Execute internal search if requested
            if ($this->searchType === self::SEARCH_INTERNAL || $this->searchType === self::SEARCH_BOTH) {
                $internalCandidates = $this->performInternalSearch();
                $totalCandidates += $internalCandidates;
            }

            // Execute external search if requested
            if ($this->searchType === self::SEARCH_EXTERNAL || $this->searchType === self::SEARCH_BOTH) {
                $externalCandidates = $this->performExternalSearch();
                $totalCandidates += $externalCandidates;
            }

            // Update execution record if this is a rerun
            $this->updateExecutionRecord($internalCandidates, $externalCandidates);

            // Create a notification for the completed search
            $this->createSearchCompletionNotification();

            // Standard log
            Log::info("SEARCH JOB: Completed search for plan ID {$this->planData['plan_id']} with {$totalCandidates} total candidates ({$internalCandidates} internal, {$externalCandidates} external)");
            
            // Detailed completion log in daily file
            $executionTime = round(microtime(true) - (defined('LARAVEL_START') ? LARAVEL_START : $_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true)), 2);
            Log::channel('daily')->info("┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓");
            Log::channel('daily')->info("┃ MASTER SEARCH JOB COMPLETED: SearchCandidatesJob                 ┃");
            Log::channel('daily')->info("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛");
            Log::channel('daily')->info("📊 SEARCH SUMMARY", [
                'plan_name' => $this->planData['plan_name'] ?? 'Unknown Plan',
                'plan_id' => $this->planData['plan_id'] ?? 'Unknown ID',
                'execution_time' => $executionTime . ' seconds',
                'internal_candidates' => $internalCandidates,
                'external_candidates' => $externalCandidates,
                'total_candidates' => $totalCandidates,
                'search_type' => $this->searchType,
                'target_roles' => $this->planData['target_roles'] ?? [],
                'user_id' => $this->user->id,
                'user_email' => $this->user->email,
                'timestamp' => now()->toDateTimeString()
            ]);

        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            $executionTime = round(microtime(true) - (defined('LARAVEL_START') ? LARAVEL_START : $_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true)), 2);

            // Update execution record with error if this is a rerun
            $this->updateExecutionRecordWithError($errorMessage);

            // Standard error log
            Log::error("SEARCH JOB: Error in search job", [
                'plan_id' => $this->planData['plan_id'] ?? 'unknown',
                'error' => $errorMessage,
                'trace' => $e->getTraceAsString()
            ]);
            
            // Detailed error log in daily file
            Log::channel('daily')->error("┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓");
            Log::channel('daily')->error("┃ MASTER SEARCH JOB FAILED: SearchCandidatesJob                    ┃");
            Log::channel('daily')->error("┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛");
            Log::channel('daily')->error("❌ SEARCH ERROR DETAILS", [
                'plan_name' => $this->planData['plan_name'] ?? 'Unknown Plan',
                'plan_id' => $this->planData['plan_id'] ?? 'Unknown ID',
                'execution_time' => $executionTime . ' seconds',
                'error' => $errorMessage,
                'search_type' => $this->searchType,
                'target_roles' => $this->planData['target_roles'] ?? [],
                'user_id' => $this->user->id,
                'user_email' => $this->user->email,
                'timestamp' => now()->toDateTimeString()
            ]);

            // Create an error notification
            $this->createErrorNotification();
        }
    }

    /**
     * Perform internal candidate search
     * Dispatches to high-priority internal search queue
     *
     * @return int Number of candidates found
     */
    protected function performInternalSearch()
    {
        Log::info("SEARCH JOB: Dispatching internal search to high-priority queue for plan '{$this->planData['plan_name']}'");

        // Use SearchQueueManager to dispatch to high-priority internal queue
        SearchQueueManager::dispatchInternalSearch($this->planData, $this->user);

        Log::info("SEARCH JOB: Internal search job dispatched to internal_search queue via SearchQueueManager");

        return 0; // Will be updated by the actual job
    }

    /**
     * Perform external candidate search
     * Dispatches to lower-priority external search queue
     *
     * @return int Number of candidates found
     */
    protected function performExternalSearch()
    {
        Log::info("SEARCH JOB: Dispatching external search to external_search queue for plan '{$this->planData['plan_name']}'");

        try {
            // Use SearchQueueManager to dispatch to lower-priority external queue
            SearchQueueManager::dispatchExternalSearch($this->planData, $this->user);

            Log::info("SEARCH JOB: External search job dispatched to external_search queue via SearchQueueManager");

            return 0; // Will be updated by the actual job
        } catch (\Exception $e) {
            // Log the error but don't fail the job
            Log::error("SEARCH JOB: External search dispatch encountered an error", [
                'plan_id' => $this->planData['plan_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return 0 to indicate no candidates were found
            return 0;
        }
    }

    /**
     * Create notification for search completion
     */
    protected function createSearchCompletionNotification()
    {
        $notificationData = [
            'user_id' => $this->user->id,
            'created_at' => now(),
        ];

        // Log completion for debugging purposes
        Log::info("SEARCH JOB: Creating completion notification", [
            'plan_id' => $this->planData['plan_id'] ?? null,
            'status' => 'completed',
            'message' => 'Succession plan candidate search completed successfully'
        ]);

        DB::table('job_queues_notification')->insert($notificationData);
    }

    /**
     * Create notification for search error
     */
    protected function createErrorNotification()
    {
        $notificationData = [
            'user_id' => $this->user->id,
            'created_at' => now(),
        ];

        // Log error for debugging purposes
        Log::info("SEARCH JOB: Creating error notification", [
            'plan_id' => $this->planData['plan_id'] ?? null,
            'status' => 'error',
            'message' => 'An error occurred during candidate search'
        ]);

        DB::table('job_queues_notification')->insert($notificationData);
    }

    /**
     * Validate and prepare plan data by ensuring all required fields are present
     * and setting default values for all optional fields
     */
    protected function validateAndPreparePlanData()
    {
        // Define required fields
        $requiredFields = ['plan_name', 'target_roles', 'companies', 'plan_id'];

        // Check required fields
        foreach ($requiredFields as $field) {
            if (!isset($this->planData[$field])) {
                throw new \Exception("Missing required field: {$field}");
            }
        }

        // Set default values for all optional fields if not present
        $this->planData['alternative_roles_titles'] = $this->planData['alternative_roles_titles'] ?? [];
        $this->planData['step_up_candidates'] = $this->planData['step_up_candidates'] ?? ['none'];
        $this->planData['minimum_tenure'] = $this->planData['minimum_tenure'] ?? null;
        $this->planData['gender'] = $this->planData['gender'] ?? 'Not required';
        $this->planData['country'] = $this->planData['country'] ?? ['none'];
        $this->planData['is_ethnicity_important'] = $this->planData['is_ethnicity_important'] ?? false;
        $this->planData['qualifications'] = $this->planData['qualifications'] ?? [];
        $this->planData['skills'] = $this->planData['skills'] ?? [];
        $this->planData['acronyms'] = $this->planData['acronyms'] ?? [];

        // Log prepared data
        Log::info("SEARCH JOB: Plan data prepared with defaults", [
            'plan_id' => $this->planData['plan_id'],
            'fields_set' => array_keys($this->planData)
        ]);
    }

    /**
     * Update execution record with completion data
     *
     * @param int $internalCandidates
     * @param int $externalCandidates
     */
    protected function updateExecutionRecord($internalCandidates, $externalCandidates)
    {
        if (isset($this->planData['execution_id'])) {
            $execution = PlanExecution::find($this->planData['execution_id']);
            if ($execution) {
                $execution->markAsCompleted($internalCandidates, $externalCandidates);

                Log::info("SEARCH JOB: Updated execution record", [
                    'execution_id' => $execution->id,
                    'internal_candidates' => $internalCandidates,
                    'external_candidates' => $externalCandidates,
                    'total_candidates' => $internalCandidates + $externalCandidates
                ]);
            }
        }
    }

    /**
     * Update execution record with error
     *
     * @param string $errorMessage
     */
    protected function updateExecutionRecordWithError($errorMessage)
    {
        if (isset($this->planData['execution_id'])) {
            $execution = PlanExecution::find($this->planData['execution_id']);
            if ($execution) {
                $execution->markAsFailed($errorMessage);

                Log::error("SEARCH JOB: Updated execution record with error", [
                    'execution_id' => $execution->id,
                    'error' => $errorMessage
                ]);
            }
        }
    }
}