<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SuccessionPlan;
use App\Models\PlanExecution;
use App\Models\PlanSchedule;
use App\Services\PlanRerunService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PlanRerunController extends Controller
{
    protected $planRerunService;

    public function __construct(PlanRerunService $planRerunService)
    {
        $this->planRerunService = $planRerunService;
    }

    /**
     * Manually rerun a succession plan
     */
    public function rerunPlan(Request $request, $planId)
    {
        try {
            $plan = SuccessionPlan::findOrFail($planId);
            $user = auth()->user();

            // Check if user has permission to rerun this plan
            if ($plan->user_id !== $user->id && !in_array($user->id, json_decode($plan->shared_with ?? '[]'))) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to rerun this plan'
                ], 403);
            }

            // Check if plan can be rerun
            if (!$this->planRerunService->canRerunPlan($plan)) {
                return response()->json([
                    'success' => false,
                    'message' => 'This plan is already running. Please wait for the current execution to complete.'
                ], 409);
            }

            // Start the rerun
            $execution = $this->planRerunService->rerunPlan($plan, $user);

            return response()->json([
                'success' => true,
                'message' => 'Plan rerun started successfully',
                'data' => [
                    'execution_id' => $execution->id,
                    'plan_id' => $plan->id,
                    'plan_name' => $plan->name,
                    'status' => $execution->status,
                    'started_at' => $execution->started_at
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("PLAN RERUN API: Failed to start plan rerun", [
                'plan_id' => $planId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to start plan rerun: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create or update a schedule for a plan
     */
    public function createSchedule(Request $request, $planId)
    {
        try {
            $plan = SuccessionPlan::findOrFail($planId);
            $user = auth()->user();

            // Check permissions
            if ($plan->user_id !== $user->id && !in_array($user->id, json_decode($plan->shared_with ?? '[]'))) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to schedule this plan'
                ], 403);
            }

            // Validate request
            $validator = Validator::make($request->all(), [
                'frequency' => 'required|in:daily,weekly,monthly',
                'frequency_value' => 'nullable|string',
                'execution_time' => 'nullable|date_format:H:i',
                'timezone' => 'nullable|string|max:50'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Additional validation based on frequency
            if ($request->frequency === 'weekly' && !in_array($request->frequency_value, ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'For weekly frequency, frequency_value must be a valid day of the week'
                ], 422);
            }

            if ($request->frequency === 'monthly') {
                $dayOfMonth = (int) $request->frequency_value;
                if ($dayOfMonth < 1 || $dayOfMonth > 28) {
                    return response()->json([
                        'success' => false,
                        'message' => 'For monthly frequency, frequency_value must be a day between 1 and 28'
                    ], 422);
                }
            }

            // Create the schedule
            $schedule = $this->planRerunService->createOrUpdateSchedule($plan, $user, $request->all());

            return response()->json([
                'success' => true,
                'message' => 'Schedule created successfully',
                'data' => [
                    'schedule_id' => $schedule->id,
                    'plan_id' => $plan->id,
                    'frequency' => $schedule->frequency,
                    'frequency_description' => $schedule->frequency_description,
                    'next_execution_at' => $schedule->next_execution_at,
                    'is_active' => $schedule->is_active
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("PLAN SCHEDULE API: Failed to create schedule", [
                'plan_id' => $planId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create schedule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get execution history for a plan
     */
    public function getExecutionHistory($planId)
    {
        try {
            $plan = SuccessionPlan::findOrFail($planId);
            $user = auth()->user();

            // Check permissions
            if ($plan->user_id !== $user->id && !in_array($user->id, json_decode($plan->shared_with ?? '[]'))) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to view this plan\'s history'
                ], 403);
            }

            $executions = $this->planRerunService->getExecutionHistory($plan, 20);

            return response()->json([
                'success' => true,
                'data' => [
                    'plan_id' => $plan->id,
                    'plan_name' => $plan->name,
                    'executions' => $executions->map(function ($execution) {
                        return [
                            'id' => $execution->id,
                            'execution_type' => $execution->execution_type,
                            'status' => $execution->status,
                            'started_at' => $execution->started_at,
                            'completed_at' => $execution->completed_at,
                            'duration' => $execution->formatted_duration,
                            'internal_candidates' => $execution->internal_candidates_found,
                            'external_candidates' => $execution->external_candidates_found,
                            'total_candidates' => $execution->total_candidates_found,
                            'error_message' => $execution->error_message,
                            'user' => [
                                'id' => $execution->user->id,
                                'name' => $execution->user->name,
                                'email' => $execution->user->email
                            ]
                        ];
                    })
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("PLAN HISTORY API: Failed to get execution history", [
                'plan_id' => $planId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get execution history: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current schedule for a plan
     */
    public function getSchedule($planId)
    {
        try {
            $plan = SuccessionPlan::findOrFail($planId);
            $user = auth()->user();

            // Check permissions
            if ($plan->user_id !== $user->id && !in_array($user->id, json_decode($plan->shared_with ?? '[]'))) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to view this plan\'s schedule'
                ], 403);
            }

            $schedule = $plan->activeSchedule;

            if (!$schedule) {
                return response()->json([
                    'success' => true,
                    'data' => null,
                    'message' => 'No active schedule found for this plan'
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'schedule_id' => $schedule->id,
                    'plan_id' => $plan->id,
                    'frequency' => $schedule->frequency,
                    'frequency_value' => $schedule->frequency_value,
                    'execution_time' => $schedule->execution_time,
                    'timezone' => $schedule->timezone,
                    'frequency_description' => $schedule->frequency_description,
                    'next_execution_at' => $schedule->next_execution_at,
                    'last_execution_at' => $schedule->last_execution_at,
                    'total_executions' => $schedule->total_executions,
                    'is_active' => $schedule->is_active
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("PLAN SCHEDULE API: Failed to get schedule", [
                'plan_id' => $planId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get schedule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Deactivate a schedule
     */
    public function deactivateSchedule($planId)
    {
        try {
            $plan = SuccessionPlan::findOrFail($planId);
            $user = auth()->user();

            // Check permissions
            if ($plan->user_id !== $user->id && !in_array($user->id, json_decode($plan->shared_with ?? '[]'))) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to modify this plan\'s schedule'
                ], 403);
            }

            $schedule = $plan->activeSchedule;

            if (!$schedule) {
                return response()->json([
                    'success' => false,
                    'message' => 'No active schedule found for this plan'
                ], 404);
            }

            $schedule->update(['is_active' => false]);

            Log::info("PLAN SCHEDULE: Deactivated schedule", [
                'schedule_id' => $schedule->id,
                'plan_id' => $plan->id,
                'user_id' => $user->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Schedule deactivated successfully'
            ]);

        } catch (\Exception $e) {
            Log::error("PLAN SCHEDULE API: Failed to deactivate schedule", [
                'plan_id' => $planId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to deactivate schedule: ' . $e->getMessage()
            ], 500);
        }
    }
}
