<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\DowntimeMonitoringService;
use App\Models\PlanSchedule;
use App\Models\PlanExecution;
use Illuminate\Support\Facades\Log;

class PlatformMonitoringController extends Controller
{
    protected $monitoringService;

    public function __construct(DowntimeMonitoringService $monitoringService)
    {
        $this->monitoringService = $monitoringService;
    }

    /**
     * Get platform monitoring dashboard data
     */
    public function dashboard()
    {
        try {
            $dashboardData = $this->monitoringService->getMonitoringDashboard();
            
            // Add additional real-time data
            $dashboardData['real_time'] = [
                'schedules_due_now' => PlanSchedule::due()->count(),
                'schedules_needing_catchup' => PlanSchedule::needsCatchUp()->count(),
                'running_executions' => PlanExecution::where('status', 'running')->count(),
                'failed_executions_today' => PlanExecution::whereDate('created_at', today())
                                                         ->where('status', 'failed')
                                                         ->count()
            ];

            return response()->json([
                'success' => true,
                'data' => $dashboardData
            ]);

        } catch (\Exception $e) {
            Log::error("MONITORING DASHBOARD: Failed to get dashboard data", [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get monitoring data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get detailed schedule health report
     */
    public function scheduleHealth()
    {
        try {
            $healthData = [
                'active_schedules' => PlanSchedule::active()->count(),
                'inactive_schedules' => PlanSchedule::where('is_active', false)->count(),
                'schedules_with_failures' => PlanSchedule::where('consecutive_failures', '>', 0)->count(),
                'schedules_needing_catchup' => PlanSchedule::needsCatchUp()->count(),
                'recent_executions' => PlanExecution::where('created_at', '>=', now()->subDays(7))
                                                   ->selectRaw('DATE(created_at) as date, status, COUNT(*) as count')
                                                   ->groupBy('date', 'status')
                                                   ->orderBy('date', 'desc')
                                                   ->get()
            ];

            // Get problematic schedules
            $problematicSchedules = PlanSchedule::withRecentFailures()
                ->with(['successionPlan', 'user'])
                ->get()
                ->map(function ($schedule) {
                    return [
                        'schedule_id' => $schedule->id,
                        'plan_name' => $schedule->successionPlan->name,
                        'user_email' => $schedule->user->email,
                        'consecutive_failures' => $schedule->consecutive_failures,
                        'last_failure_at' => $schedule->last_failure_at,
                        'frequency' => $schedule->frequency_description,
                        'is_active' => $schedule->is_active
                    ];
                });

            $healthData['problematic_schedules'] = $problematicSchedules;

            return response()->json([
                'success' => true,
                'data' => $healthData
            ]);

        } catch (\Exception $e) {
            Log::error("SCHEDULE HEALTH: Failed to get schedule health data", [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get schedule health data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Trigger manual health check
     */
    public function triggerHealthCheck()
    {
        try {
            $healthData = $this->monitoringService->checkPlatformHealth();

            return response()->json([
                'success' => true,
                'message' => 'Health check completed',
                'data' => $healthData
            ]);

        } catch (\Exception $e) {
            Log::error("HEALTH CHECK: Manual health check failed", [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Health check failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Trigger manual catch-up for missed executions
     */
    public function triggerCatchUp(Request $request)
    {
        try {
            $maxConcurrent = $request->input('max_concurrent', 2);
            
            // Run the catch-up command
            \Artisan::call('plans:run-scheduled', [
                '--catch-up' => true,
                '--max-concurrent' => $maxConcurrent
            ]);

            $output = \Artisan::output();

            Log::info("MANUAL CATCH-UP: Triggered by user", [
                'user_id' => auth()->id(),
                'max_concurrent' => $maxConcurrent,
                'output' => $output
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Catch-up process initiated',
                'output' => $output
            ]);

        } catch (\Exception $e) {
            Log::error("MANUAL CATCH-UP: Failed to trigger catch-up", [
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to trigger catch-up: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reset failed schedule (clear consecutive failures)
     */
    public function resetFailedSchedule(Request $request, $scheduleId)
    {
        try {
            $schedule = PlanSchedule::findOrFail($scheduleId);
            
            // Check permissions
            $user = auth()->user();
            if ($schedule->user_id !== $user->id && !$user->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to reset this schedule'
                ], 403);
            }

            // Reset failure counters
            $schedule->update([
                'consecutive_failures' => 0,
                'last_failure_at' => null,
                'is_active' => true
            ]);

            Log::info("SCHEDULE RESET: Failed schedule reset by user", [
                'schedule_id' => $scheduleId,
                'plan_id' => $schedule->succession_plan_id,
                'user_id' => $user->id,
                'previous_failures' => $schedule->consecutive_failures
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Schedule reset successfully'
            ]);

        } catch (\Exception $e) {
            Log::error("SCHEDULE RESET: Failed to reset schedule", [
                'schedule_id' => $scheduleId,
                'user_id' => auth()->id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to reset schedule: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get execution logs for debugging
     */
    public function getExecutionLogs(Request $request)
    {
        try {
            $limit = $request->input('limit', 50);
            $status = $request->input('status');
            
            $query = PlanExecution::with(['successionPlan', 'user'])
                                 ->orderBy('created_at', 'desc')
                                 ->limit($limit);

            if ($status) {
                $query->where('status', $status);
            }

            $executions = $query->get()->map(function ($execution) {
                return [
                    'id' => $execution->id,
                    'plan_name' => $execution->successionPlan->name,
                    'user_email' => $execution->user->email,
                    'execution_type' => $execution->execution_type,
                    'status' => $execution->status,
                    'started_at' => $execution->started_at,
                    'completed_at' => $execution->completed_at,
                    'duration' => $execution->formatted_duration,
                    'candidates_found' => $execution->total_candidates_found,
                    'error_message' => $execution->error_message
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $executions
            ]);

        } catch (\Exception $e) {
            Log::error("EXECUTION LOGS: Failed to get execution logs", [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get execution logs: ' . $e->getMessage()
            ], 500);
        }
    }
}
