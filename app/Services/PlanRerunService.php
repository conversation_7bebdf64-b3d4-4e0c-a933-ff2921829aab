<?php

namespace App\Services;

use App\Models\SuccessionPlan;
use App\Models\PlanExecution;
use App\Models\PlanSchedule;
use App\Models\SuccessRequirements;
use App\Jobs\SearchCandidatesJob;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PlanRerunService
{
    /**
     * Rerun a succession plan manually
     *
     * @param SuccessionPlan $plan
     * @param \App\Models\User $user
     * @return PlanExecution
     */
    public function rerunPlan(SuccessionPlan $plan, $user)
    {
        Log::info("PLAN RERUN: Starting manual rerun for plan", [
            'plan_id' => $plan->id,
            'plan_name' => $plan->name,
            'user_id' => $user->id
        ]);

        // Create execution record
        $execution = PlanExecution::create([
            'succession_plan_id' => $plan->id,
            'user_id' => $user->id,
            'execution_type' => 'manual',
            'status' => 'pending',
            'execution_metadata' => $this->buildExecutionMetadata($plan)
        ]);

        // Build plan data from original plan and requirements
        $planData = $this->buildPlanDataFromExisting($plan, $execution);

        // Mark execution as started
        $execution->markAsStarted();

        try {
            // Dispatch search job with execution tracking
            SearchCandidatesJob::dispatch($planData, $user, 'both')
                ->onQueue('default');

            Log::info("PLAN RERUN: Search job dispatched successfully", [
                'execution_id' => $execution->id,
                'plan_id' => $plan->id
            ]);

        } catch (\Exception $e) {
            $execution->markAsFailed($e->getMessage());
            
            Log::error("PLAN RERUN: Failed to dispatch search job", [
                'execution_id' => $execution->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }

        return $execution;
    }

    /**
     * Create or update a schedule for a plan
     *
     * @param SuccessionPlan $plan
     * @param \App\Models\User $user
     * @param array $scheduleData
     * @return PlanSchedule
     */
    public function createOrUpdateSchedule(SuccessionPlan $plan, $user, array $scheduleData)
    {
        // Deactivate any existing schedules for this plan
        PlanSchedule::where('succession_plan_id', $plan->id)
                   ->update(['is_active' => false]);

        // Create new schedule
        $schedule = PlanSchedule::create([
            'succession_plan_id' => $plan->id,
            'user_id' => $user->id,
            'frequency' => $scheduleData['frequency'],
            'frequency_value' => $scheduleData['frequency_value'] ?? null,
            'execution_time' => $scheduleData['execution_time'] ?? '09:00:00',
            'timezone' => $scheduleData['timezone'] ?? 'UTC',
            'is_active' => true
        ]);

        // Calculate first execution time
        $schedule->calculateNextExecution();

        Log::info("PLAN SCHEDULE: Created new schedule", [
            'schedule_id' => $schedule->id,
            'plan_id' => $plan->id,
            'frequency' => $schedule->frequency,
            'next_execution' => $schedule->next_execution_at
        ]);

        return $schedule;
    }

    /**
     * Execute a scheduled plan run
     *
     * @param PlanSchedule $schedule
     * @return PlanExecution
     */
    public function executeScheduledRun(PlanSchedule $schedule)
    {
        $plan = $schedule->successionPlan;
        $user = $schedule->user;

        Log::info("PLAN SCHEDULE: Executing scheduled run", [
            'schedule_id' => $schedule->id,
            'plan_id' => $plan->id,
            'plan_name' => $plan->name
        ]);

        // Create execution record
        $execution = PlanExecution::create([
            'succession_plan_id' => $plan->id,
            'user_id' => $user->id,
            'execution_type' => 'scheduled',
            'status' => 'pending',
            'execution_metadata' => $this->buildExecutionMetadata($plan, $schedule)
        ]);

        // Build plan data
        $planData = $this->buildPlanDataFromExisting($plan, $execution);

        // Mark execution as started
        $execution->markAsStarted();

        try {
            // Dispatch search job
            SearchCandidatesJob::dispatch($planData, $user, 'both')
                ->onQueue('default');

            // Mark schedule execution as completed
            $schedule->markExecutionCompleted();

            Log::info("PLAN SCHEDULE: Scheduled execution dispatched successfully", [
                'execution_id' => $execution->id,
                'schedule_id' => $schedule->id,
                'next_execution' => $schedule->next_execution_at
            ]);

        } catch (\Exception $e) {
            $execution->markAsFailed($e->getMessage());
            
            Log::error("PLAN SCHEDULE: Failed to execute scheduled run", [
                'execution_id' => $execution->id,
                'schedule_id' => $schedule->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }

        return $execution;
    }

    /**
     * Build plan data from existing plan and requirements
     *
     * @param SuccessionPlan $plan
     * @param PlanExecution $execution
     * @return array
     */
    protected function buildPlanDataFromExisting(SuccessionPlan $plan, PlanExecution $execution)
    {
        // Get plan requirements
        $requirements = SuccessRequirements::where('plan_id', $plan->id)->get();

        // Build the plan data structure expected by SearchCandidatesJob
        $planData = [
            'plan_id' => $plan->id,
            'execution_id' => $execution->id, // Add execution ID for tracking
            'plan_name' => $plan->name,
            'description' => $plan->description,
            'minimum_tenure' => $plan->minimum_Experience,
            'is_ethnicity_important' => $plan->ethnicity,
            'target_roles' => [],
            'alternative_roles_titles' => [],
            'step_up_candidates' => [],
            'companies' => [],
            'country' => [],
            'gender' => 'Not required',
            'qualifications' => [],
            'skills' => []
        ];

        // Extract data from requirements
        foreach ($requirements as $requirement) {
            switch ($requirement->requirement_type) {
                case 'target_roles':
                    $planData['target_roles'][] = $requirement->requirement_value;
                    break;
                case 'alternative_roles':
                    $planData['alternative_roles_titles'][] = $requirement->requirement_value;
                    break;
                case 'step_up_candidates':
                    $planData['step_up_candidates'][] = $requirement->requirement_value;
                    break;
                case 'companies':
                    $planData['companies'][] = $requirement->requirement_value;
                    break;
                case 'countries':
                    $planData['country'][] = $requirement->requirement_value;
                    break;
                case 'qualifications':
                    $planData['qualifications'][] = $requirement->requirement_value;
                    break;
                case 'skills':
                    $planData['skills'][] = $requirement->requirement_value;
                    break;
                case 'gender':
                    $planData['gender'] = $requirement->requirement_value;
                    break;
            }
        }

        return $planData;
    }

    /**
     * Build execution metadata
     *
     * @param SuccessionPlan $plan
     * @param PlanSchedule|null $schedule
     * @return array
     */
    protected function buildExecutionMetadata(SuccessionPlan $plan, PlanSchedule $schedule = null)
    {
        $metadata = [
            'plan_name' => $plan->name,
            'plan_status' => $plan->status,
            'rerun_timestamp' => now()->toISOString()
        ];

        if ($schedule) {
            $metadata['schedule'] = [
                'frequency' => $schedule->frequency,
                'frequency_value' => $schedule->frequency_value,
                'execution_time' => $schedule->execution_time,
                'timezone' => $schedule->timezone
            ];
        }

        return $metadata;
    }

    /**
     * Get execution history for a plan
     *
     * @param SuccessionPlan $plan
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getExecutionHistory(SuccessionPlan $plan, $limit = 10)
    {
        return $plan->executions()
                   ->with('user')
                   ->orderBy('created_at', 'desc')
                   ->limit($limit)
                   ->get();
    }

    /**
     * Check if a plan can be rerun
     *
     * @param SuccessionPlan $plan
     * @return bool
     */
    public function canRerunPlan(SuccessionPlan $plan)
    {
        // Check if there's already a running execution
        $runningExecution = $plan->executions()
                                 ->where('status', 'running')
                                 ->exists();

        return !$runningExecution;
    }
}
