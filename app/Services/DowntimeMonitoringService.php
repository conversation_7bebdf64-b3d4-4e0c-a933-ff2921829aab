<?php

namespace App\Services;

use App\Models\PlanSchedule;
use App\Models\PlanExecution;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class DowntimeMonitoringService
{
    const DOWNTIME_THRESHOLD_MINUTES = 30;
    const CRITICAL_FAILURE_THRESHOLD = 5;
    const MONITORING_CACHE_KEY = 'platform_monitoring';

    /**
     * Check platform health and detect downtime scenarios
     */
    public function checkPlatformHealth()
    {
        $healthData = [
            'timestamp' => now(),
            'downtime_detected' => false,
            'missed_executions' => 0,
            'failed_schedules' => 0,
            'alerts_sent' => []
        ];

        // Check for missed executions
        $missedExecutions = $this->detectMissedExecutions();
        $healthData['missed_executions'] = $missedExecutions['count'];

        // Check for failed schedules
        $failedSchedules = $this->detectFailedSchedules();
        $healthData['failed_schedules'] = $failedSchedules['count'];

        // Detect potential downtime
        if ($this->detectDowntime()) {
            $healthData['downtime_detected'] = true;
            $healthData['alerts_sent'][] = $this->sendDowntimeAlert($missedExecutions, $failedSchedules);
        }

        // Check for critical failures
        if ($failedSchedules['count'] >= self::CRITICAL_FAILURE_THRESHOLD) {
            $healthData['alerts_sent'][] = $this->sendCriticalFailureAlert($failedSchedules);
        }

        // Store monitoring data
        Cache::put(self::MONITORING_CACHE_KEY, $healthData, now()->addHours(24));

        return $healthData;
    }

    /**
     * Detect missed executions that need attention
     */
    protected function detectMissedExecutions()
    {
        $missedSchedules = PlanSchedule::needsCatchUp()
            ->with(['successionPlan', 'user'])
            ->get();

        $missedData = [
            'count' => $missedSchedules->count(),
            'schedules' => [],
            'total_missed' => 0
        ];

        foreach ($missedSchedules as $schedule) {
            $missedCount = $schedule->calculateMissedExecutions();
            $missedData['total_missed'] += $missedCount;
            
            $missedData['schedules'][] = [
                'schedule_id' => $schedule->id,
                'plan_id' => $schedule->succession_plan_id,
                'plan_name' => $schedule->successionPlan->name,
                'user_email' => $schedule->user->email,
                'missed_count' => $missedCount,
                'next_execution_at' => $schedule->next_execution_at,
                'last_attempted_at' => $schedule->last_attempted_at
            ];
        }

        if ($missedData['count'] > 0) {
            Log::warning("MISSED EXECUTIONS DETECTED", $missedData);
        }

        return $missedData;
    }

    /**
     * Detect schedules with consecutive failures
     */
    protected function detectFailedSchedules()
    {
        $failedSchedules = PlanSchedule::withRecentFailures()
            ->with(['successionPlan', 'user'])
            ->get();

        $failureData = [
            'count' => $failedSchedules->count(),
            'schedules' => []
        ];

        foreach ($failedSchedules as $schedule) {
            $failureData['schedules'][] = [
                'schedule_id' => $schedule->id,
                'plan_id' => $schedule->succession_plan_id,
                'plan_name' => $schedule->successionPlan->name,
                'user_email' => $schedule->user->email,
                'consecutive_failures' => $schedule->consecutive_failures,
                'last_failure_at' => $schedule->last_failure_at,
                'is_active' => $schedule->is_active
            ];
        }

        if ($failureData['count'] > 0) {
            Log::error("SCHEDULE FAILURES DETECTED", $failureData);
        }

        return $failureData;
    }

    /**
     * Detect potential platform downtime
     */
    protected function detectDowntime()
    {
        $lastRunFile = storage_path('app/last_scheduled_run.txt');
        
        if (!file_exists($lastRunFile)) {
            return false;
        }

        $lastRun = Carbon::parse(file_get_contents($lastRunFile));
        $minutesSinceLastRun = $lastRun->diffInMinutes(now());

        return $minutesSinceLastRun > self::DOWNTIME_THRESHOLD_MINUTES;
    }

    /**
     * Send downtime alert to administrators
     */
    protected function sendDowntimeAlert($missedExecutions, $failedSchedules)
    {
        $alertData = [
            'type' => 'downtime',
            'timestamp' => now(),
            'missed_executions' => $missedExecutions['count'],
            'failed_schedules' => $failedSchedules['count'],
            'total_missed' => $missedExecutions['total_missed']
        ];

        // Log critical alert
        Log::critical("PLATFORM DOWNTIME ALERT", $alertData);

        // Send email alert (implement based on your email system)
        try {
            $this->sendEmailAlert('downtime', $alertData);
            $alertData['email_sent'] = true;
        } catch (\Exception $e) {
            Log::error("Failed to send downtime email alert", ['error' => $e->getMessage()]);
            $alertData['email_sent'] = false;
        }

        return $alertData;
    }

    /**
     * Send critical failure alert
     */
    protected function sendCriticalFailureAlert($failedSchedules)
    {
        $alertData = [
            'type' => 'critical_failures',
            'timestamp' => now(),
            'failed_count' => $failedSchedules['count'],
            'schedules' => $failedSchedules['schedules']
        ];

        Log::critical("CRITICAL SCHEDULE FAILURES ALERT", $alertData);

        try {
            $this->sendEmailAlert('critical_failures', $alertData);
            $alertData['email_sent'] = true;
        } catch (\Exception $e) {
            Log::error("Failed to send critical failure email alert", ['error' => $e->getMessage()]);
            $alertData['email_sent'] = false;
        }

        return $alertData;
    }

    /**
     * Send email alert (customize based on your email system)
     */
    protected function sendEmailAlert($type, $data)
    {
        // Get admin emails from config
        $adminEmails = config('app.admin_emails', ['<EMAIL>']);

        $subject = match($type) {
            'downtime' => '🚨 Succession Planning Platform Downtime Detected',
            'critical_failures' => '⚠️ Critical Schedule Failures in Succession Planning',
            default => 'Platform Alert'
        };

        $message = $this->buildAlertMessage($type, $data);

        // Use your preferred email method
        foreach ($adminEmails as $email) {
            // Example using Laravel's Mail facade
            // Mail::raw($message, function ($mail) use ($email, $subject) {
            //     $mail->to($email)->subject($subject);
            // });
            
            // For now, just log the alert
            Log::info("EMAIL ALERT WOULD BE SENT", [
                'to' => $email,
                'subject' => $subject,
                'message' => $message
            ]);
        }
    }

    /**
     * Build alert message content
     */
    protected function buildAlertMessage($type, $data)
    {
        $message = "Succession Planning Platform Alert\n";
        $message .= "Time: " . $data['timestamp']->format('Y-m-d H:i:s') . "\n\n";

        switch ($type) {
            case 'downtime':
                $message .= "DOWNTIME DETECTED:\n";
                $message .= "- Missed Executions: {$data['missed_executions']}\n";
                $message .= "- Failed Schedules: {$data['failed_schedules']}\n";
                $message .= "- Total Missed: {$data['total_missed']}\n\n";
                $message .= "Action Required: Check platform status and run catch-up command if needed.\n";
                $message .= "Command: php artisan plans:run-scheduled --catch-up\n";
                break;

            case 'critical_failures':
                $message .= "CRITICAL SCHEDULE FAILURES:\n";
                $message .= "- Failed Schedules: {$data['failed_count']}\n\n";
                $message .= "Affected Plans:\n";
                foreach ($data['schedules'] as $schedule) {
                    $message .= "- {$schedule['plan_name']} (ID: {$schedule['plan_id']}) - {$schedule['consecutive_failures']} failures\n";
                }
                $message .= "\nAction Required: Review schedule configurations and system logs.\n";
                break;
        }

        return $message;
    }

    /**
     * Get monitoring dashboard data
     */
    public function getMonitoringDashboard()
    {
        $cachedData = Cache::get(self::MONITORING_CACHE_KEY, []);
        
        return [
            'last_check' => $cachedData['timestamp'] ?? null,
            'platform_status' => $cachedData['downtime_detected'] ?? false ? 'DOWN' : 'UP',
            'missed_executions' => $cachedData['missed_executions'] ?? 0,
            'failed_schedules' => $cachedData['failed_schedules'] ?? 0,
            'recent_alerts' => $cachedData['alerts_sent'] ?? [],
            'active_schedules' => PlanSchedule::active()->count(),
            'total_executions_today' => PlanExecution::whereDate('created_at', today())->count(),
            'success_rate_today' => $this->calculateTodaySuccessRate()
        ];
    }

    /**
     * Calculate today's execution success rate
     */
    protected function calculateTodaySuccessRate()
    {
        $todayExecutions = PlanExecution::whereDate('created_at', today());
        $total = $todayExecutions->count();
        
        if ($total === 0) {
            return 100;
        }
        
        $successful = $todayExecutions->where('status', 'completed')->count();
        return round(($successful / $total) * 100, 1);
    }
}
