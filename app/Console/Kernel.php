<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        $schedule->job(new \App\Jobs\CleanupOldLogFiles)->monthly();

        // Run scheduled plan executions every 15 minutes with resilience
        $schedule->command('plans:run-scheduled --max-concurrent=3')
                 ->everyFifteenMinutes()
                 ->withoutOverlapping(30) // 30 minute overlap protection
                 ->runInBackground()
                 ->onFailure(function () {
                     \Log::critical('SCHEDULER FAILURE: plans:run-scheduled command failed');
                 });

        // Run catch-up for missed executions every hour
        $schedule->command('plans:run-scheduled --catch-up --max-concurrent=2')
                 ->hourly()
                 ->withoutOverlapping(60)
                 ->runInBackground()
                 ->when(function () {
                     // Only run if there are schedules that need catch-up
                     return \App\Models\PlanSchedule::needsCatchUp()->exists();
                 });

        // Platform health monitoring every 30 minutes
        $schedule->call(function () {
                     $monitor = new \App\Services\DowntimeMonitoringService();
                     $monitor->checkPlatformHealth();
                 })
                 ->everyThirtyMinutes()
                 ->name('platform-health-check')
                 ->withoutOverlapping();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
