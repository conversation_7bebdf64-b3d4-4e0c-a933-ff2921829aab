<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\PlanSchedule;
use App\Services\PlanRerunService;
use Illuminate\Support\Facades\Log;

class RunScheduledPlans extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'plans:run-scheduled {--dry-run : Show what would be executed without actually running}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Execute scheduled succession plan reruns';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No plans will be executed');
        }

        // Get all due schedules
        $dueSchedules = PlanSchedule::due()->with(['successionPlan', 'user'])->get();

        if ($dueSchedules->isEmpty()) {
            $this->info('✅ No scheduled plans are due for execution');
            return 0;
        }

        $this->info("📋 Found {$dueSchedules->count()} scheduled plan(s) due for execution:");

        $planRerunService = new PlanRerunService();
        $successCount = 0;
        $errorCount = 0;

        foreach ($dueSchedules as $schedule) {
            $plan = $schedule->successionPlan;
            $user = $schedule->user;

            $this->line("  • Plan: {$plan->name} (ID: {$plan->id})");
            $this->line("    Schedule: {$schedule->frequency_description}");
            $this->line("    User: {$user->name} ({$user->email})");
            $this->line("    Due: {$schedule->next_execution_at->format('Y-m-d H:i:s')}");

            if ($isDryRun) {
                $this->line("    [DRY RUN] Would execute now");
                continue;
            }

            try {
                // Execute the scheduled run
                $execution = $planRerunService->executeScheduledRun($schedule);
                
                $this->info("    ✅ Execution started (ID: {$execution->id})");
                $successCount++;

                Log::info("SCHEDULED EXECUTION: Successfully started scheduled plan execution", [
                    'schedule_id' => $schedule->id,
                    'execution_id' => $execution->id,
                    'plan_id' => $plan->id,
                    'plan_name' => $plan->name,
                    'user_id' => $user->id,
                    'next_execution' => $schedule->next_execution_at
                ]);

            } catch (\Exception $e) {
                $this->error("    ❌ Failed to execute: {$e->getMessage()}");
                $errorCount++;

                Log::error("SCHEDULED EXECUTION: Failed to start scheduled plan execution", [
                    'schedule_id' => $schedule->id,
                    'plan_id' => $plan->id,
                    'plan_name' => $plan->name,
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            $this->line('');
        }

        if (!$isDryRun) {
            $this->info("📊 Execution Summary:");
            $this->info("  ✅ Successful: {$successCount}");
            if ($errorCount > 0) {
                $this->error("  ❌ Failed: {$errorCount}");
            }

            // Log summary
            Log::info("SCHEDULED EXECUTION SUMMARY", [
                'total_schedules_due' => $dueSchedules->count(),
                'successful_executions' => $successCount,
                'failed_executions' => $errorCount,
                'timestamp' => now()->toDateTimeString()
            ]);
        }

        return $errorCount > 0 ? 1 : 0;
    }
}
