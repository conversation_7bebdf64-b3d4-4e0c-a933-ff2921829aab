<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\PlanSchedule;
use App\Services\PlanRerunService;
use Illuminate\Support\Facades\Log;

class RunScheduledPlans extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'plans:run-scheduled
                            {--dry-run : Show what would be executed without actually running}
                            {--catch-up : Process missed executions from downtime}
                            {--max-concurrent=3 : Maximum concurrent executions to prevent system overload}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Execute scheduled succession plan reruns';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        $catchUp = $this->option('catch-up');
        $maxConcurrent = (int) $this->option('max-concurrent');

        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No plans will be executed');
        }

        // Check for platform downtime recovery
        $this->checkForDowntimeRecovery();

        // Get schedules to process
        $dueSchedules = collect();
        $missedSchedules = collect();

        // Regular due schedules
        $regularDue = PlanSchedule::due()->with(['successionPlan', 'user'])->get();
        $dueSchedules = $dueSchedules->merge($regularDue);

        // Missed executions that need catch-up (if enabled)
        if ($catchUp || $this->shouldAutoCatchUp()) {
            $missed = PlanSchedule::needsCatchUp()->with(['successionPlan', 'user'])->get();
            $missedSchedules = $missed;

            if ($missed->isNotEmpty()) {
                $this->warn("🔄 Found {$missed->count()} schedule(s) with missed executions for catch-up");
            }
        }

        $totalSchedules = $dueSchedules->count() + $missedSchedules->count();

        if ($totalSchedules === 0) {
            $this->info('✅ No scheduled plans are due for execution');
            return 0;
        }

        // Apply rate limiting to prevent system overload
        $schedulesToProcess = $this->applyRateLimiting($dueSchedules, $missedSchedules, $maxConcurrent);

        if ($schedulesToProcess->count() < $totalSchedules) {
            $this->warn("⚠️  Rate limiting applied: Processing {$schedulesToProcess->count()} of {$totalSchedules} schedules");
        }

        $this->info("📋 Found {$dueSchedules->count()} scheduled plan(s) due for execution:");

        $planRerunService = new PlanRerunService();
        $successCount = 0;
        $errorCount = 0;

        foreach ($dueSchedules as $schedule) {
            $plan = $schedule->successionPlan;
            $user = $schedule->user;

            $this->line("  • Plan: {$plan->name} (ID: {$plan->id})");
            $this->line("    Schedule: {$schedule->frequency_description}");
            $this->line("    User: {$user->name} ({$user->email})");
            $this->line("    Due: {$schedule->next_execution_at->format('Y-m-d H:i:s')}");

            if ($isDryRun) {
                $this->line("    [DRY RUN] Would execute now");
                continue;
            }

            try {
                // Execute the scheduled run
                $execution = $planRerunService->executeScheduledRun($schedule);
                
                $this->info("    ✅ Execution started (ID: {$execution->id})");
                $successCount++;

                Log::info("SCHEDULED EXECUTION: Successfully started scheduled plan execution", [
                    'schedule_id' => $schedule->id,
                    'execution_id' => $execution->id,
                    'plan_id' => $plan->id,
                    'plan_name' => $plan->name,
                    'user_id' => $user->id,
                    'next_execution' => $schedule->next_execution_at
                ]);

            } catch (\Exception $e) {
                $this->error("    ❌ Failed to execute: {$e->getMessage()}");
                $errorCount++;

                Log::error("SCHEDULED EXECUTION: Failed to start scheduled plan execution", [
                    'schedule_id' => $schedule->id,
                    'plan_id' => $plan->id,
                    'plan_name' => $plan->name,
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }

            $this->line('');
        }

        if (!$isDryRun) {
            $this->info("📊 Execution Summary:");
            $this->info("  ✅ Successful: {$successCount}");
            if ($errorCount > 0) {
                $this->error("  ❌ Failed: {$errorCount}");
            }

            // Log summary
            Log::info("SCHEDULED EXECUTION SUMMARY", [
                'total_schedules_due' => $dueSchedules->count(),
                'successful_executions' => $successCount,
                'failed_executions' => $errorCount,
                'timestamp' => now()->toDateTimeString()
            ]);
        }

        return $errorCount > 0 ? 1 : 0;
    }

    /**
     * Check for platform downtime recovery scenarios
     */
    protected function checkForDowntimeRecovery()
    {
        // Check if this is the first run after potential downtime
        $lastRunFile = storage_path('app/last_scheduled_run.txt');
        $currentTime = now();

        if (file_exists($lastRunFile)) {
            $lastRun = \Carbon\Carbon::parse(file_get_contents($lastRunFile));
            $timeSinceLastRun = $lastRun->diffInMinutes($currentTime);

            // If more than 30 minutes since last run, potential downtime
            if ($timeSinceLastRun > 30) {
                $this->warn("⚠️  Potential downtime detected: {$timeSinceLastRun} minutes since last run");
                $this->warn("   Last run: {$lastRun->format('Y-m-d H:i:s')}");

                // Log downtime event
                Log::warning("PLATFORM DOWNTIME DETECTED", [
                    'last_run' => $lastRun->toDateTimeString(),
                    'current_time' => $currentTime->toDateTimeString(),
                    'downtime_minutes' => $timeSinceLastRun,
                    'auto_recovery' => true
                ]);

                // Check for missed schedules
                $missedCount = PlanSchedule::needsCatchUp()->count();
                if ($missedCount > 0) {
                    $this->warn("   Found {$missedCount} schedule(s) that may have missed executions");
                }
            }
        }

        // Update last run timestamp
        file_put_contents($lastRunFile, $currentTime->toDateTimeString());
    }

    /**
     * Determine if auto catch-up should be enabled
     */
    protected function shouldAutoCatchUp()
    {
        // Auto catch-up if there are schedules that haven't been attempted recently
        $stalledSchedules = PlanSchedule::active()
            ->where('next_execution_at', '<', now()->subHours(2))
            ->whereNull('last_attempted_at')
            ->count();

        return $stalledSchedules > 0;
    }

    /**
     * Apply rate limiting to prevent system overload
     */
    protected function applyRateLimiting($dueSchedules, $missedSchedules, $maxConcurrent)
    {
        $allSchedules = collect();

        // Prioritize regular due schedules over catch-up
        $regularCount = min($dueSchedules->count(), $maxConcurrent);
        $allSchedules = $allSchedules->merge($dueSchedules->take($regularCount));

        // Add catch-up schedules if we have capacity
        $remainingCapacity = $maxConcurrent - $regularCount;
        if ($remainingCapacity > 0 && $missedSchedules->isNotEmpty()) {
            $catchUpCount = min($missedSchedules->count(), $remainingCapacity);
            $allSchedules = $allSchedules->merge($missedSchedules->take($catchUpCount));
        }

        return $allSchedules;
    }

    /**
     * Execute a single schedule with enhanced error handling
     */
    protected function executeScheduleWithResilience($schedule, $planRerunService, $isCatchUp = false)
    {
        $plan = $schedule->successionPlan;
        $user = $schedule->user;

        try {
            // Mark attempt
            $schedule->markExecutionAttempt(false); // Assume failure until success

            // Execute the scheduled run
            $execution = $planRerunService->executeScheduledRun($schedule);

            // Mark success
            $schedule->markExecutionAttempt(true);

            $executionType = $isCatchUp ? 'CATCH-UP' : 'SCHEDULED';
            $this->info("    ✅ {$executionType} execution started (ID: {$execution->id})");

            Log::info("SCHEDULED EXECUTION: Successfully started {$executionType} plan execution", [
                'schedule_id' => $schedule->id,
                'execution_id' => $execution->id,
                'plan_id' => $plan->id,
                'plan_name' => $plan->name,
                'user_id' => $user->id,
                'execution_type' => $executionType,
                'next_execution' => $schedule->next_execution_at
            ]);

            return true;

        } catch (\Exception $e) {
            // Mark failure (already done above, but update with error details)
            $schedule->markExecutionAttempt(false, $e->getMessage());

            $this->error("    ❌ Failed to execute: {$e->getMessage()}");

            Log::error("SCHEDULED EXECUTION: Failed to start scheduled plan execution", [
                'schedule_id' => $schedule->id,
                'plan_id' => $plan->id,
                'plan_name' => $plan->name,
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'consecutive_failures' => $schedule->consecutive_failures,
                'trace' => $e->getTraceAsString()
            ]);

            return false;
        }
    }
}
