<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SuccessionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'minimum_Experience',
        'step_up',
        'ethnicity',
        'tagged_individual',
        'status',
        'shared_with',
        'user_id',
        'age',
        'last_opened',
        'candidate_status',
        'mover'
    ];

    public function successPeople()
    {
        return $this->hasMany(SuccessPeople::class, 'plan_id');
    }

    public function user()
    {   
        return $this->belongsTo(user::class);
    }

    public function requirements()
    {
        return $this->hasMany(SuccessRequirements::class, 'plan_id');
    }

    public function executions()
    {
        return $this->hasMany(PlanExecution::class);
    }

    public function schedules()
    {
        return $this->hasMany(PlanSchedule::class);
    }

    public function activeSchedule()
    {
        return $this->hasOne(PlanSchedule::class)->where('is_active', true);
    }

    public function latestExecution()
    {
        return $this->hasOne(PlanExecution::class)->latest();
    }

    public function completedExecutions()
    {
        return $this->hasMany(PlanExecution::class)->where('status', 'completed');
    }
}
