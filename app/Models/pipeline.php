<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class pipeline extends Model
{
    use HasFactory;

    protected $fillable = [
        'plan_id',
        'execution_id',
        'job_id',
        'type',
        'user_id',
        'people_id',
        'first_name',
        'last_name',
        'middle_name',
        'other_name',
        'gender',
        'diverse',
        'location',
        'summary',
        'linkedinURL',
        'latest_role',
        'company_id',
        'company_name',
        'start_date',
        'end_date',
        'tenure',
        'function',
        'division',
        'exco',
        'seniority',
        'career_history',
        'educational_history',
        'skills',
        'languages',
        'other_tags',
        'skills_match',
        'education_match',
        'location_match',
        'role_match',
        'gender_match',
        'tenure_match',
        'total_score',
        'people_type',
        'readiness',
        'country',
        'city',
        'status',
        'mover'
    ];



    public function user()
    {
        return $this->belongsTo(user::class);
    }

    public function successionPlan()
    {
        return $this->belongsTo(SuccessionPlan::class, 'plan_id');
    }

    public function execution()
    {
        return $this->belongsTo(PlanExecution::class);
    }

}
