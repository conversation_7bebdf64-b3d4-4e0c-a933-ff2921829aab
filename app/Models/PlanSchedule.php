<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PlanSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'succession_plan_id',
        'user_id',
        'frequency',
        'frequency_value',
        'execution_time',
        'timezone',
        'is_active',
        'next_execution_at',
        'last_execution_at',
        'total_executions'
    ];

    protected $casts = [
        'execution_time' => 'datetime:H:i:s',
        'next_execution_at' => 'datetime',
        'last_execution_at' => 'datetime',
        'is_active' => 'boolean'
    ];

    /**
     * Get the succession plan that owns this schedule
     */
    public function successionPlan()
    {
        return $this->belongsTo(SuccessionPlan::class);
    }

    /**
     * Get the user who created this schedule
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Calculate and set the next execution time
     */
    public function calculateNextExecution()
    {
        $timezone = $this->timezone ?: 'UTC';
        $executionTime = $this->execution_time ?: '09:00:00';
        
        $now = Carbon::now($timezone);
        $nextExecution = null;

        switch ($this->frequency) {
            case 'daily':
                $nextExecution = $now->copy()->addDay()->setTimeFromTimeString($executionTime);
                break;
                
            case 'weekly':
                $dayOfWeek = $this->frequency_value ?: 'monday';
                $nextExecution = $now->copy()->next($dayOfWeek)->setTimeFromTimeString($executionTime);
                break;
                
            case 'monthly':
                $dayOfMonth = (int) ($this->frequency_value ?: 1);
                $nextExecution = $now->copy()->addMonth()->day($dayOfMonth)->setTimeFromTimeString($executionTime);
                break;
        }

        if ($nextExecution) {
            $this->update(['next_execution_at' => $nextExecution->utc()]);
        }

        return $nextExecution;
    }

    /**
     * Mark execution as completed and calculate next execution
     */
    public function markExecutionCompleted()
    {
        $this->increment('total_executions');
        $this->update(['last_execution_at' => now()]);
        $this->calculateNextExecution();
    }

    /**
     * Check if schedule is due for execution
     */
    public function isDue()
    {
        if (!$this->is_active || !$this->next_execution_at) {
            return false;
        }

        return $this->next_execution_at <= now();
    }

    /**
     * Scope for active schedules
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for schedules due for execution
     */
    public function scopeDue($query)
    {
        return $query->active()
                    ->whereNotNull('next_execution_at')
                    ->where('next_execution_at', '<=', now());
    }

    /**
     * Get human readable frequency description
     */
    public function getFrequencyDescriptionAttribute()
    {
        switch ($this->frequency) {
            case 'daily':
                return 'Daily at ' . Carbon::parse($this->execution_time)->format('g:i A');
                
            case 'weekly':
                $day = ucfirst($this->frequency_value ?: 'monday');
                return "Weekly on {$day} at " . Carbon::parse($this->execution_time)->format('g:i A');
                
            case 'monthly':
                $day = $this->frequency_value ?: 1;
                $suffix = $this->getOrdinalSuffix($day);
                return "Monthly on the {$day}{$suffix} at " . Carbon::parse($this->execution_time)->format('g:i A');
                
            default:
                return 'Unknown frequency';
        }
    }

    /**
     * Get ordinal suffix for day of month
     */
    private function getOrdinalSuffix($number)
    {
        $ends = ['th', 'st', 'nd', 'rd', 'th', 'th', 'th', 'th', 'th', 'th'];
        if ((($number % 100) >= 11) && (($number % 100) <= 13)) {
            return 'th';
        } else {
            return $ends[$number % 10];
        }
    }
}
