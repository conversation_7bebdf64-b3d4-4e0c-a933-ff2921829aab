<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PlanSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'succession_plan_id',
        'user_id',
        'frequency',
        'frequency_value',
        'execution_time',
        'timezone',
        'is_active',
        'next_execution_at',
        'last_execution_at',
        'last_attempted_at',
        'total_executions',
        'missed_executions',
        'consecutive_failures',
        'last_failure_at',
        'catch_up_enabled',
        'max_catch_up_executions'
    ];

    protected $casts = [
        'execution_time' => 'datetime:H:i:s',
        'next_execution_at' => 'datetime',
        'last_execution_at' => 'datetime',
        'last_attempted_at' => 'datetime',
        'last_failure_at' => 'datetime',
        'is_active' => 'boolean',
        'catch_up_enabled' => 'boolean'
    ];

    /**
     * Get the succession plan that owns this schedule
     */
    public function successionPlan()
    {
        return $this->belongsTo(SuccessionPlan::class);
    }

    /**
     * Get the user who created this schedule
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Calculate and set the next execution time
     */
    public function calculateNextExecution()
    {
        $timezone = $this->timezone ?: 'UTC';
        $executionTime = $this->execution_time ?: '09:00:00';
        
        $now = Carbon::now($timezone);
        $nextExecution = null;

        switch ($this->frequency) {
            case 'daily':
                $nextExecution = $now->copy()->addDay()->setTimeFromTimeString($executionTime);
                break;
                
            case 'weekly':
                $dayOfWeek = $this->frequency_value ?: 'monday';
                $nextExecution = $now->copy()->next($dayOfWeek)->setTimeFromTimeString($executionTime);
                break;
                
            case 'monthly':
                $dayOfMonth = (int) ($this->frequency_value ?: 1);
                $nextExecution = $now->copy()->addMonth()->day($dayOfMonth)->setTimeFromTimeString($executionTime);
                break;
        }

        if ($nextExecution) {
            $this->update(['next_execution_at' => $nextExecution->utc()]);
        }

        return $nextExecution;
    }

    /**
     * Mark execution as completed and calculate next execution
     */
    public function markExecutionCompleted()
    {
        $this->increment('total_executions');
        $this->update(['last_execution_at' => now()]);
        $this->calculateNextExecution();
    }

    /**
     * Check if schedule is due for execution
     */
    public function isDue()
    {
        if (!$this->is_active || !$this->next_execution_at) {
            return false;
        }

        return $this->next_execution_at <= now();
    }

    /**
     * Check if schedule has missed executions that need catch-up
     */
    public function hasMissedExecutions()
    {
        if (!$this->is_active || !$this->next_execution_at || !$this->catch_up_enabled) {
            return false;
        }

        // Calculate how many executions were missed
        $missedCount = $this->calculateMissedExecutions();
        return $missedCount > 0 && $missedCount <= $this->max_catch_up_executions;
    }

    /**
     * Calculate number of missed executions
     */
    public function calculateMissedExecutions()
    {
        if (!$this->next_execution_at) {
            return 0;
        }

        $now = now();
        $nextExecution = $this->next_execution_at;

        // If next execution is in the future, no missed executions
        if ($nextExecution > $now) {
            return 0;
        }

        $missedCount = 0;
        $currentTime = $nextExecution->copy();

        // Calculate missed executions based on frequency
        while ($currentTime <= $now && $missedCount < $this->max_catch_up_executions) {
            $missedCount++;

            // Move to next scheduled time
            switch ($this->frequency) {
                case 'daily':
                    $currentTime->addDay();
                    break;
                case 'weekly':
                    $currentTime->addWeek();
                    break;
                case 'monthly':
                    $currentTime->addMonth();
                    break;
            }
        }

        return $missedCount;
    }

    /**
     * Mark execution attempt (successful or failed)
     */
    public function markExecutionAttempt($success = true, $errorMessage = null)
    {
        $this->update(['last_attempted_at' => now()]);

        if ($success) {
            $this->increment('total_executions');
            $this->update([
                'last_execution_at' => now(),
                'consecutive_failures' => 0,
                'last_failure_at' => null
            ]);
            $this->calculateNextExecution();
        } else {
            $this->increment('consecutive_failures');
            $this->increment('missed_executions');
            $this->update(['last_failure_at' => now()]);

            // Disable schedule if too many consecutive failures
            if ($this->consecutive_failures >= 5) {
                $this->update(['is_active' => false]);
                \Log::warning("SCHEDULE DISABLED: Too many consecutive failures", [
                    'schedule_id' => $this->id,
                    'plan_id' => $this->succession_plan_id,
                    'consecutive_failures' => $this->consecutive_failures
                ]);
            }
        }
    }

    /**
     * Scope for active schedules
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for schedules due for execution
     */
    public function scopeDue($query)
    {
        return $query->active()
                    ->whereNotNull('next_execution_at')
                    ->where('next_execution_at', '<=', now());
    }

    /**
     * Scope for schedules with missed executions that need catch-up
     */
    public function scopeNeedsCatchUp($query)
    {
        return $query->active()
                    ->where('catch_up_enabled', true)
                    ->whereNotNull('next_execution_at')
                    ->where('next_execution_at', '<', now())
                    ->whereRaw('(missed_executions < max_catch_up_executions OR missed_executions IS NULL)');
    }

    /**
     * Scope for schedules with recent failures
     */
    public function scopeWithRecentFailures($query)
    {
        return $query->where('consecutive_failures', '>', 0)
                    ->whereNotNull('last_failure_at')
                    ->where('last_failure_at', '>', now()->subDays(7));
    }

    /**
     * Get human readable frequency description
     */
    public function getFrequencyDescriptionAttribute()
    {
        switch ($this->frequency) {
            case 'daily':
                return 'Daily at ' . Carbon::parse($this->execution_time)->format('g:i A');
                
            case 'weekly':
                $day = ucfirst($this->frequency_value ?: 'monday');
                return "Weekly on {$day} at " . Carbon::parse($this->execution_time)->format('g:i A');
                
            case 'monthly':
                $day = $this->frequency_value ?: 1;
                $suffix = $this->getOrdinalSuffix($day);
                return "Monthly on the {$day}{$suffix} at " . Carbon::parse($this->execution_time)->format('g:i A');
                
            default:
                return 'Unknown frequency';
        }
    }

    /**
     * Get ordinal suffix for day of month
     */
    private function getOrdinalSuffix($number)
    {
        $ends = ['th', 'st', 'nd', 'rd', 'th', 'th', 'th', 'th', 'th', 'th'];
        if ((($number % 100) >= 11) && (($number % 100) <= 13)) {
            return 'th';
        } else {
            return $ends[$number % 10];
        }
    }
}
