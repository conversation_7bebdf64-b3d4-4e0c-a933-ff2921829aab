<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PlanExecution extends Model
{
    use HasFactory;

    protected $fillable = [
        'succession_plan_id',
        'user_id',
        'execution_type',
        'status',
        'started_at',
        'completed_at',
        'internal_candidates_found',
        'external_candidates_found',
        'total_candidates_found',
        'error_message',
        'execution_metadata'
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'execution_metadata' => 'array'
    ];

    /**
     * Get the succession plan that owns this execution
     */
    public function successionPlan()
    {
        return $this->belongsTo(SuccessionPlan::class);
    }

    /**
     * Get the user who initiated this execution
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the pipeline candidates for this execution
     */
    public function pipelineCandidates()
    {
        return $this->hasMany(Pipeline::class, 'execution_id');
    }

    /**
     * Mark execution as started
     */
    public function markAsStarted()
    {
        $this->update([
            'status' => 'running',
            'started_at' => now()
        ]);
    }

    /**
     * Mark execution as completed
     */
    public function markAsCompleted($internalCount = 0, $externalCount = 0)
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'internal_candidates_found' => $internalCount,
            'external_candidates_found' => $externalCount,
            'total_candidates_found' => $internalCount + $externalCount
        ]);
    }

    /**
     * Mark execution as failed
     */
    public function markAsFailed($errorMessage = null)
    {
        $this->update([
            'status' => 'failed',
            'completed_at' => now(),
            'error_message' => $errorMessage
        ]);
    }

    /**
     * Get execution duration in seconds
     */
    public function getDurationAttribute()
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }
        
        return $this->completed_at->diffInSeconds($this->started_at);
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute()
    {
        $duration = $this->duration;
        if (!$duration) {
            return 'N/A';
        }

        $hours = floor($duration / 3600);
        $minutes = floor(($duration % 3600) / 60);
        $seconds = $duration % 60;

        if ($hours > 0) {
            return sprintf('%dh %dm %ds', $hours, $minutes, $seconds);
        } elseif ($minutes > 0) {
            return sprintf('%dm %ds', $minutes, $seconds);
        } else {
            return sprintf('%ds', $seconds);
        }
    }

    /**
     * Scope for completed executions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for failed executions
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for running executions
     */
    public function scopeRunning($query)
    {
        return $query->where('status', 'running');
    }
}
