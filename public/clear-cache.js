// Clear service worker cache
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(function(registrations) {
        for(let registration of registrations) {
            registration.unregister();
            console.log('Service Worker unregistered:', registration);
        }
    });
}

// Clear browser cache
if ('caches' in window) {
    caches.keys().then(function(names) {
        for (let name of names) {
            caches.delete(name);
            console.log('Cache deleted:', name);
        }
    });
}

console.log('Cache clearing completed. Please refresh the page.');
