<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PlanRerunController;
use App\Services\ErrorHandling\ErrorLogger;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Client-side error logging endpoint
Route::post('/log-client-error', function (Request $request) {
    $data = $request->all();
    
    // Validate required fields before accessing them
    $type = $data['type'] ?? 'Unknown Error';
    $url = $data['url'] ?? 'Unknown URL';
    
    // Log the client-side error with appropriate context
    ErrorLogger::log(
        "Client Error: {$type} on {$url}",
        $data,
        ErrorLogger::CATEGORY_UI,
        'warning'
    );
    
    return response()->json(['status' => 'logged']);
});

// Plan rerun API routes
Route::middleware('auth:sanctum')->group(function () {
    Route::prefix('plans/{planId}')->group(function () {
        Route::post('/rerun', [PlanRerunController::class, 'rerunPlan']);
        Route::post('/schedule', [PlanRerunController::class, 'createSchedule']);
        Route::get('/schedule', [PlanRerunController::class, 'getSchedule']);
        Route::delete('/schedule', [PlanRerunController::class, 'deactivateSchedule']);
        Route::get('/executions', [PlanRerunController::class, 'getExecutionHistory']);
    });
});